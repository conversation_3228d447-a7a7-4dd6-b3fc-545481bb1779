package sg.cfg
{
    public class ServerURLcfg_fs
    {
        public function ServerURLcfg_fs()
        {
            
        }
        public var appVersionTxt:String = "v_1_0"
        public var appFunVersion:String = "1";
        public var CC_test:Object = { // 本地开发测试用
                http_url: HelpConfig.serverAddress || 'http://*************:8500/gateway/',
                // http_url: 'http://qh.ptkill.com/gateway/',
                // area_check:"http://gw.niuwank.com/ccc/"
                // http_url: 'http://res-kol.r2game.com/gateway/'
                // net_cfg_url: 'http://sg3.ptkill.com/gateway/',
                // http_url: 'http://hk.ptkill.com/gateway/',
                // http_url:'http://************/gateway/'
                // assets_base_url:"http://cdn2.ptkill.com/static/h5/meng52/",
                // assets_version_url:"http://cdn2.ptkill.com/static/h5/meng52/",
                // net_cfg_url:"http://sg.ptkill.com/static/h5/"
                "copyright": "抵制不良游戏,拒绝盗版游戏。注意自我保护,谨防受骗上当。\n适度游戏益脑,沉迷游戏伤身。合理安排时间,享受健康生活。",
                'des': "测试配置"}; 
        public var CC_test_local:Object = { // 本地开发测试手机版本配置
                http_url: 'http://*************:8500/gateway/',
                // area_check:"http://gw.niuwank.com/ccc/"
                // http_url: 'http://res-kol.r2game.com/gateway/'
                // http_url: 'http://sg3.ptkill.com/gateway/'
                // http_url: 'http://hk.ptkill.com/gateway/',
                // http_url:'http://************/gateway/'
                assets_base_url: "http://192.168.1.66/release/test/",
                assets_version_url: "http://192.168.1.66/release/test/",
                // net_cfg_url:"http://sg.ptkill.com/static/h5/"
                'des': "测试配置"};
        public var CC_server:Object = {
            // http_url: 'http://192.168.1.128:8888/gateway/',		//龚子敬的服务器三七苹果
            // http_url: 'http://hk.ptkill.com:9988/gateway/',
            // http_url: 'http://192.168.101.121:8500/gateway/',
            // http_url: 'http://sg3.ptkill.com/gateway/',
            // http_url: 'https://hk.ptkill.com/gateway/',
            // http_url: 'http://hk.ptkill.com:9987/gateway/',
            // http_url: 'http://qh.ptkill.com/gateway/',
            // assets_base_url:"http://192.168.1.66/bin/h5/",
            // assets_version_url:"http://d25tqlozljq1fr.cloudfront.net/static/h5/tw_hk/"
                net_cfg_url:"http://zsh.akbing.com/static/h5/",
                // http_url:'http://************/gateway/'
                'des': "标准配置"};                            
    }
}