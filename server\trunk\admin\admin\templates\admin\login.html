{% extends 'admin/base.html' %}
{% block content %}
<script type='text/javascript'>
    $(document).ready(function () {
        $('#img_verify_code').hide();
    });
    function getSmsCode() {
        var username = $('#username').val();
        var password = $('#password').val();

        $nx.request.postfs('sms_verify_code/', {username: username, password: password}, function (data) {
            if (data.verify_type === 'img_code') {
                $('#sms_code').hide();
                $('#img_verify_code').show();
                $('#img_verify_code').html('<img src="{{settings.BASE_URL}}/sg_backend_2024/verify_code/?v={{nocache}}">')
            } else {
                $('#img_verify_code').hide();
                $('#img_verify_code').html('')
                alert('登录验证码已发送！');
            }
        })
    };
</script>
    <div>
        <p style="font-size: 500%; margin: 0 0 0 0; color: {{ settings.ADMIN_STYLE_CONFIG.ADMIN_COLOR_TITLE }}">{{ settings.ADMIN_STYLE_CONFIG.TITLE }}</p>
    </div>
<div>
    <form action='{{settings.BASE_URL}}/sg_backend_2024/' method='post' target='_top'>
        {% if error %}
            {% ifequal error 'null_username' %}
            <font color="red">用户不存在</font>
            {% endifequal %}
            {% ifequal error 'err_userpass' %}
            <font color="red">用户名或密码错误</font>
            {% endifequal %}
            {% ifequal error 'err_verify_code' %}
            <font color="red">验证码错误或已过期，请重新获取</font>
            {% endifequal %}
        {%endif%}
        <p>登录名: <input name='username' id='username'/></p>
        <p>密码: <input name='password' type='password' id='password'/></p>
        <p id="img_verify_code"></p>
        <p>验证码: <input name='verify_code' style="width: 70px;"/>&nbsp;&nbsp;<button id="sms_code" type="button" onclick="getSmsCode()">获取验证码</button></p>
        <p><input type='submit' value='登录' />
    </form>

    <font class="Apple-style-span" color="{{settings.ADMIN_STYLE_CONFIG.ADMIN_COLOR_TITLE}}" size="7"><b>
            {{settings.ADMIN_STYLE_CONFIG.APP_NAME}}({{settings.ADMIN_STYLE_CONFIG.APP_NAME_CN}})</b></font>

</div>

{% endblock %}
