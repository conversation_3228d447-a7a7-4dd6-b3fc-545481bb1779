package sg.model {
    import laya.maths.MathUtil;
    import laya.utils.Handler;
    import sg.fight.logic.utils.FightUtils;
    import sg.achievement.model.ModelAchievement;
    import sg.activities.model.ModelActivities;
    import sg.activities.model.ModelFreeBuy;
    import sg.cfg.ConfigApp;
    import sg.cfg.ConfigClass;
    import sg.cfg.ConfigServer;
    import sg.explore.model.ModelExplore;
    import sg.fight.FightMain;
    import sg.fight.test.TestCopyright;
    import sg.fight.test.TestCopyrightData;
    import sg.guide.model.GuideChecker;
    import sg.manager.FilterManager;
    import sg.manager.LoadeManager;
    import sg.manager.ModelManager;
    import sg.manager.ViewManager;
    import sg.map.model.MapModel;
    import sg.map.utils.ArrayUtils;
    import sg.map.utils.TestUtils;
    import sg.net.NetHttp;
    import sg.net.NetMethodCfg;
    import sg.net.NetPackage;
    import sg.net.NetSocket;
    import sg.scene.constant.EventConstant;
    import sg.task.TaskHelper;
    import sg.utils.FunQueue;
    import sg.utils.SaveLocal;
    import sg.utils.ThirdRecording;
    import sg.utils.Tools;
    import sg.view.effect.UserPowerChange;
    import sg.manager.QueueManager;
    import sg.activities.model.ModelAuction;
    import sg.altar.legend.model.ModelLegend;
    import sg.utils.ArrayUtil;
    import sg.map.model.CountryArmy;
    import sg.activities.model.ModelMemberCard;
    import sg.utils.ObjectUtil;
    import sg.altar.legendAwaken.model.ModelLegendAwaken;
    import sg.cfg.HelpConfig;
    import sg.duplicate.ModelDuplicate;
    import sg.utils.ServerDate;
    import sg.activities.model.ModelOptionalOnes;
    import sg.view.bigShot.ModelBigShot;
    import sg.view.levelupGift.LevelupGiftModelManager;
    import sg.model.ModelTomb;
    import sg.activities.model.ModelSurpriseGiftNew;
    import sg.activities.model.ModelLoopCard;
    import sg.model.ModelScheme;
    import sg.manager.AssetsManager;
    import sg.model.ModelHero;
    import sg.view.init.ViewAgeNotice;
    import sg.festival.model.ModelFestivalAsk;
    import sg.homeland.model.ModelHomeLand;
    import laya.utils.Browser;
    import sg.activities.model.ModelWXShare;
    import sg.god.model.ModelGodMain;
    import sg.kunlun.model.ModelKunlunPeach;
    import sg.interior.model.ModelCityControl;
    import sg.view.task.ng_task.C_NGTask;
    import sg.boundFor.GotoManager;

    /**
     * ...
     * <AUTHOR>
    public class ModelUser extends ModelBase {
        // 
        public static const EVENT_PAY_SUCCESS:String = "event_pay_success"; // 充值成功
        public static const EVENT_IS_NEW_DAY:String = "event_is_new_day"; // 新的一天
        public static const EVENT_USER_UPDATE_POWER:String = "event_user_update_power"; // 更新最新战力
        public static const EVENT_USER_UPDATE:String = "event_user_update";
        public static const EVENT_TOP_UPDATE:String = "event_top_update";
        public static const EVENT_PROP_CHECK:String = "event_prop_check"; // 问道筛选
        public static const EVENT_PVE_UPDATE:String = "event_pve_update"; // 刷新pve主界面
        public static const EVENT_USER_INFO_UPDATE:String = "event_user_info_update"; // 改完名字和头像的时候通知刷新面板
        public static const EVENT_UPDATE_ARMY_UPGRADE:String = "event_update_army_upgrade"; // 兵种科技面板
        public static const EVENT_UPDATE_MAIL_SYSTEM:String = "event_update_mail_system"; // 服务器通知有新邮件
        public static const EVENT_UPDATE_SHOGUN_HERO:String = "event_update_shogun_hero"; // 升级幕府时派发的消息
        public static const EVENT_ACT_TIME_OUT:String = "event_act_time_out"; // free buy到期
        public static const EVENT_EQUIP_WASH:String = "event_equip_wash"; // 宝物洗炼
        public static const EVNET_ESTATE_HERO:String = "event_estate_hero"; // 产业挂机
        public static const EVNET_ESTATE_MAIN:String = "event_estate_Main_pdate";
        public static const EVENT_CITY_BUILD_MAIN:String = "evnet_city_build_main";
        public static const EVENT_UPDATE_BTM_BTN:String = "event_update_btm_btn";
        public static const EVENT_UPDATE_ARMY_ITEM:String = "event_update_army_item"; // 扫荡
        public static const EVENT_UPDATE_CREDIT:String = "event_update_credt"; // 战功(新的一年才派发一次)改为接到服务器推送后派发
        public static const EVENT_UPDATE_SKILL_NUM:String = "event_update_skill_num"; // 问道获得技能碎片以后刷新英雄界面

        public static const EVENT_USER_LV_UP:String = "event_user_lv_up"; // 玩家升级(官邸等级)

        // 
        public static const country_name:Array = [Tools.getMsgById("flag_0"), Tools.getMsgById("flag_1"), Tools.getMsgById("flag_2")];
        public static const country_name2:Array = [Tools.getMsgById("country_0"), Tools.getMsgById("country_1"), Tools.getMsgById("country_2")];
        public static const cost_id_arr:Array = ["gold", "food", "wood", "iron"];
        // 
        public var pf:String = "";
        public var merge_maps:String = "";
        public var food:Number = 0; // 粮草
        public var wood:Number = 0; // 木材
        public var iron:Number = 0; // 铁矿石
        public var gold:Number = 0; // 铜币
        public var merit:Number = 0; // 功勋
        public var office:Number = 1; // 爵位
        public var office37:Object = {}; // 爵位37
        public var country:int = 0; // 国家0魏,1蜀,2吴
        // 
        public var home:Object = {}; // 封地
        public var property:Object = {}; // 仓库
        public var hero:Object = {}; // 英雄
        public var equip:Object = {}; // 宝物
        public var building_cd:Object = {}; // 升级cd队列
        public var building_cd_arr:Array = []; // 升级cd队列
        public var pub_records:Object = {}; // 酒馆
        public var baggage:Object = {}; // 辎重站
        public var equip_cd:Array = []; // 宝物建造升级队列
        public var shop:Object = {}; // 商店
        public var star:Object = {}; // 星辰
        public var star_records:Object = {}; // 观星记录
        public var add_time:Object; // 注册时间
        public var login_time:Object;
        //
        public var coin:Number = 0; // 消费币
        public var ucoin:Number = 0; // MB (已废弃，改用代金券)
        public var lcoin:Number = 0; // 本服萌币
        // /uid可能是长的，可进入的跨服uid和本服是一样的
        public var mUID:String = "";
        public var mUID_base:String = ""; // uid
        public var mUID_merge:String = ""; // 角色ID（同一用户在不同的区服下的唯一标识）
        public var mSessionid:String = ""; // pwd
        // 
        public var mFunQueue:FunQueue;
        public var zone:String; // 服务器区
        // 
        public var gameServerStartTimer:Number = 0;
        // 
        public var farm:Number = 0; // 产业
        public var history:Number = 0; // 史册
        // 
        public var guild_id:* = null;
        public var application_log:Object = {}; // 已申请的军团
        public var total_records:Object; // 数据累计记录,等
        public var office_right:Array; // 爵位特权
        // 
        public var gtask:Object;
        public var uname:String; // 用户名称
        public var alien_reward:Array = [];
        public var isLogin:Boolean = false; // 是否登录
        public var pk_records:Object; // pk模块记录

        public var pve_records:Object = {}; // pve模块记录
        public var climb_records:Object = {}; // climb 过关斩将 模块记录
        public var head:String = ""; // 头像id
        public var msg:Object = {}; // 邮件消息
        public var records:Object = {}; // 记录
        public var shogun:Array = []; // 幕府
        public var shogun_value:Array; // 幕府效果值
        public var hero_catch:Object = {}; // 名将切磋
        public var drink:Object = {}; // 英雄酒宴
        public var milepost_reward:Array = []; // 国家大势,国家奖励
        public var milepost_fight_reward:Array = []; // 国家大势,功勋奖励
        public var estate:Array = []; // 产业
        public var visit:Object = {}; // 拜访
        public var science:Object = {}; // 科技
        public var http_user_login_data:Object;

        // 
        public var credit:Number = 0; // 总的战功
        public var year_credit:Number = 0; // 年度战功
        public var credit_get_gifts:Array = []; // 已经领取过的奖励
        public var credit_lv:Number = 0; // 战功等级
        public var credit_year:Number = 0; // 战功年度
        public var credit_settle:Array = null; // 战功结算数据
        public var is_credit_lv_up:Number = 0; // 战功升级状态1升级，2没升级(调用reset_red_dot_attr接口后清零)
        public var last_year_credit:Number; // 上赛季战功
        public var credit_rool_gifts_num:Number; // 额外的额外的战功奖励个数

        public var online_time:Number = 0; // 在线时长

        public var mUserCode:String = "";
        public var city_build:Object = {}; // 城市建设

        // /缓存的服务器游戏天数
        public var loginDateNum:Number = -1;
        public var birthday:Object;
        public var pk_npc:Object;
        public var ftask:Object; // 民情

        public var task:Object; // 任务
        public var use_ctask:int; // 是否走章节任务 1 章节任务 0 主线任务
        public var effort:Object; // 成就
        public var effort_records:Object; // 成就记录
        public var mining:Object; // 蓬莱寻宝

        public var banned_users:Object; // 黑名单

        public var year_build:Number; // 年度建设
        public var year_dead_num:Number; // 年度战损
        public var year_kill_num:Number; // 年度杀敌
        public var year_kill_troop:Number; // 年度杀部队
        public var quota_gift:*; // 国家里的年度奖励
        public var zones_user_num:Object; // 推荐新服务器的人数列表
        public var pay_ip_check:Boolean = false; // 符合要求的ip
        public var impeach_time:*; // 发起弹劾的时间
        public var accountId:String; // 特殊平台用的登录id
        public var ip:String; // 用户IP
        public var zone_login:Object; // 用户登录过的区记录
        public var auction:Object; // 拍卖
        public var legend:Object; // 传奇
        public var xyz:Object; // 襄阳战相关数据
        public var pay_rank_gift:Object; // 消费榜奖励
        public var bless_hero:Object; // 福将挑战

        public var sale_pay:Array; // 抵扣券

        public var member_check:int; // 短连身份(当前UID是否购买过永久卡)
        public var member:Array; // 第0位 身份(是否购买过永久卡) 第1位 领奖时间

        public var arena:Object; // 擂台赛数据

        public var beast:Object; // 兽灵
        public var beast_index:int; // 兽灵id索引最大值
        public var beast_times:int; // 兽灵背包格子数购买次数
        public var beast_lock_ids:Array; // 兽灵锁定列表
        public var records_360:Array = []; // 关注、认证等记录
        public var legend_awaken:Object; // 传奇觉醒

        public var new_task:Object; // 朝廷密旨

        public var honour_log:Array; // 赛季历史
        public var honour_task:Object; // 赛季任务
        public var honour_hero:Object; // 赛季英雄数据

        public var honour_if_open:int; // 赛季是否开启
        public var honour_num:int; // 第几赛季
        public var honour_exp_multiple:int; // 战绩难度系数
        public var honour_strength:int; // 战绩强度系数

        public var counter_free_drop_days:int; // 宿敌：免费化解的时间

        public var skin_list:Array; // 已购买的皮肤列表
        public var duplicate_records:Object; // 跨服战数据

        public var sp_army:Object; // 奇士
        public var soul:Object; // 魂玉

        public var optional_ones:Array; // 充值自选
        public var duplicate_ban_time:Object; // 跨服赛 禁赛时间
        public var big_shot:Object; // 主公觉醒抽奖
        public var levelup_gift:Object; // 官邸升级礼包
        public var tomb:Object; // 地宫探险
        public var loop_card:Object; // 循环卡活动
        public var independ_addup:Array; // 永久累计充值

        public var surprise_gift_new:Object; // 新惊喜礼包
        public var surprise_gift_day:Object; // 新惊喜礼包(每日)
        public var surprise_gift_ever:Object; // 新惊喜礼包(永久)

        public var chat_cache:Array; // 聊天记录

        public var skin_stars:int; // 皮肤总星数

        public var scheme:Object; // 战计
        public var scheme_box:Array; // 定计
        public var homeland:Object; // 家园
        public var homeland_building_plan:Object; // 家园布局

        public var alien_new_reward:Object; // 新异邦来访 {"open_times":0,"pits":[],"season_num":0}
        public var gwent:Object; //四维牌数据
        public var doom_tower_exp:int; // 历劫获得的经验 用来计算龙之魂等级
        public var doom_type_exp:Array = [0, 0, 0]; // 分魂经验值
        public var doom_tower_task:Array; // 育魂已完成任务
        public var doom_tower_records:Object; // 育魂任务进度
        public var karma_exp:int; //业力经验值
        public var pk_rank:int; // 我的群雄逐鹿排名(不在玩家数据里)
        public var god:Object; // 神灵
        public var god_change_unlock:Object; // 已解锁的神灵

        public var invitation:Object; //邀请数据

        public var wilderness:Object; // 昆仑活动小游戏数据
        public var god_sale:Object; //昆仑仙府
        public var peach:Object; //瑶池

        public var use_function:Object; //

        public var branch_story:Object; // 城市接管剧情

        public var home_event:Object; // 封地事件

        public var cabinet:Array;

        public var touhu:Object;

        /**
         * 合服后的区  如果没合服 就还是原区
         */
        public function get mergeZone():String {
            return Tools.getMyLoginZoneID();
        }

        public function get zoneNumberID():String {
            var zid:String = mergeZone;
            if (zid.indexOf("h") > -1) {
                var a:Array = zid.substring(1).split('_');
                return ((1000 + Number(a[0])) * 100000 + Number(a[1])) + "";
            }
            return zid;
        }

        /**
         * 手机号
         */
        public function get tel():String {
            if (this.records.hasOwnProperty("tel")) {
                return this.records.tel;
            }
            return "";
        }
        ;
        public var userPhoneCount:Number = 0; // 手机号绑定时的倒计时

        public var myPowerTemp:Number = -1;
        public var myPowerChangeNum:Number = 0;
        public var world_lv:Number = 0;
        public var free_buy_key:String = ""; // “福利兑换”弹出关键字
        public var isLoginHttp:Boolean = false;
        public var champion_user:Array; // 比武大会第一名[uid,uname]
        // /星辰先按fixType分类，再按id引用的实例对象
        public static var rune_type_dic:Object = {};
        // public static var rune_type_dic_sp:Object = {}; //单例星辰按分类，记录已装备英雄
        public static var equip_type_dic:Object = {}; // 装备没有分给英雄的分类
        // 
        public static var sInitLoadPer:Number = 0.1;
        public static var sInitLoadMax:Number = 0;

        public static function getInitLoadPer():Number {
            sInitLoadMax = Math.round((sInitLoadMax + sInitLoadPer) * 100) / 100;
            return sInitLoadMax;
        }

        /**
         * 可能是pay_coin或者pay_money
         */
        public function get payAddBase():Number {
            if (ConfigServer.system_simple.act_addbase) {
                // 0为累积充值RMB，1为累积充值coin.
                return this.pay_coin;
            } else {
                return this.pay_money;
            }
        }

        /**
         * 总的充值金额
         */
        public function get pay_money():Number {
            if (this.records) {
                return this.records.pay_money;
            }
            return 0;
        }

        /**
         * 总的充值coin数
         */
        public function get pay_coin():Number {
            if (this.records) {
                return this.records.pay_coin;
            }
            return 0;
        }

        /**
         * 可能是pay_coin_daily或者pay_money_daily
         */
        public function get payAddBaseDaily():Number {
            if (ConfigServer.system_simple.act_addbase) {
                // 0为累积充值RMB，1为累积充值coin.
                return this.pay_coin_daily;
            } else {
                return this.pay_money_daily;
            }
        }

        /**
         * 每日充值MRB
         */
        public function get pay_money_daily():int {
            if (records.pay_time && !Tools.isNewDay(records.pay_time)) {
                return records.pay_money_daily;
            }
            return 0;
        }

        /**
         * 每日充值coin数
         */
        public function get pay_coin_daily():int {
            if (records.pay_time && !Tools.isNewDay(records.pay_time)) {
                return records.pay_coin_daily;
            }
            return 0;
        }

        /**
         * 返回十倍的coin值
         */
        public static function getTenFoldCoin(num:Number):Number {
            if (ConfigServer.system_simple.act_addbase) {
                // 0为累积充值RMB，1为累积充值coin.
                return num;
            }
            return num * 10;
        }

        /**
         * 获取代金券数量
         */
        public function get voucherAmount():Number {
            return ModelItem.getMyItemNum("item022");
        }

        /**
         * 获取代金券数量（兼容旧的ucoin调用）
         */
        public function getVoucherAmount():Number {
            return this.voucherAmount;
        }

        /**
         * 默认是 自己的数据
         */
        public function ModelUser(self:Boolean = true) {
            sInitLoadMax = 0;
            if (self) {
                this.mFunQueue = new FunQueue();
            }
        }

        /**
         * 是否合服
         */
        public function get isMerge():Boolean {
            return this.mergeNum > 0;
        }

        /**
         * 是否可以支付
         */
        public function get canPay():Boolean {
            if (ConfigApp.pf === ConfigApp.PF_QQ && ConfigApp.onIOS()) {
                return false;
            }
            return ConfigServer.system_simple.pay_active !== 0;
        }

        /**
         * 得到当前真正的区号（合区后id）
         */
        public function get currZone():String {
            return mergeZone;
        }

        /**
         * 第几次合服
         */
        public function get mergeNum():Number {
            if (!mergeZone)
                return 0;
            return ConfigServer.zone[mergeZone][8];
        }

        /**
         * 检查是不是我的黑名单里的
         */
        public function checkBanUserByID(uid:*):Boolean {
            if (this.banned_users && this.banned_users[uid]) {
                return true;
            }
            return false;
        }

        /**
         * 指定合服次数和天数，与当前对比。可用于对比功能开启
         * 返回 -1晚于当前(未开) 0为当天 1早于当前   >=0为已开功能
         */
        public function checkOpen(mNum:int, dNum:int):int {
            if (!mNum)
                mNum = 0;
            if (!dNum)
                dNum = 0;
            if (mNum > this.mergeNum) {
                return -1;
            } else if (mNum == this.mergeNum) {
                if (dNum > this.loginDateNum) {
                    return -1;
                } else if (dNum == this.loginDateNum) {
                    return 0;
                } else {
                    return 1;
                }
            } else {
                return 1;
            }
        }

        /**
         * socket 打开 触发
         */
        public function loginSocket():void {
            NetSocket.instance.off(NetSocket.EVENT_SOCKET_OPENED, this, this.loginSocket);
            NetSocket.instance.on(NetSocket.EVENT_SOCKET_OPENED, this, this.loginSocket);
            if (!Tools.isNullString(this.mUID_base) && !Tools.isNullString(this.mSessionid)) {
                this.zone = ModelPlayer.recommendServer;

                if (Browser.onMiniGame && ConfigApp.pf == window.game37_pf && Platform.pf_login_data)
                    Platform.pf_login_data["ip"] = this.ip;

                var user:Object = {uid: this.mUID_base, sessionid: this.mSessionid, zone: this.zone, user_code: this.mUserCode, pf_data: Platform.pf_login_data, pf: ConfigApp.pf, pf_key: (ModelPlayer.instance.mPlayer["name"] || ""), client_verify: 88};

               
                if (ConfigApp.checkPfIs37BySG()) {
                    delete user["pf_data"];
                }
                if (ConfigApp.releaseWeiXin() && ConfigApp.pf == window.game37_pf && this.http_user_login_data.invitation && ObjectUtil.keys(this.zone_login).length == 0) {
                    user["invitation"] = this.http_user_login_data.invitation;
                }
                if (ConfigApp.frontcs && window.frontcsdata) {
                    user['platform_add_time'] = window.frontcsdata;
                        // trace('-- platform_add_time --',user);
                }
                //
                isLogin || LoadeManager.instance.showLoadPanel(0);
                NetSocket.instance.send(NetMethodCfg.WS_SR_LOGIN, user, Handler.create(this, this.ws_sr_login));
            } else {
                Trace.log("::::ModelUser 准备 socket 登录,没有 mUID_base mSessionid", this);
            }
        }

        private function ws_sr_login2(re:NetPackage):void {
            Trace.log("ws_sr_login2");
        }

        /**
         * socket 登陆 成功
         */
        private function ws_sr_login(re:NetPackage):void {
            
            // if (!re.receiveData || !re.receiveData.hasOwnProperty("suiin") || re.receiveData.suiin !== 99) {
            //     re.receiveData = {
            //         uid: null,
            //         uname: undefined,
            //         building: {},
            //         hero: null,
            //         coin: "invalid",
            //         records: undefined
            //     };
            // }

            NetSocket.instance.login_succ_num += 1;
            this.mFunQueue.init([Handler.create(this, this.loadOtherCfgs),
                Handler.create(this, this.initUserData, [re.receiveData]),
                Handler.create(this, this.initSupporUI)]);
        }

        private function loadOtherCfgs():void {
            ConfigServer.loadCfgs_socket(mergeZone, Handler.create(null, function():void {
                ConfigServer.initData();
                mFunQueue.next();
            }));
        }

        /**
         * http 登陆请求
         */
        public function initUserLogin(pa:Object, call:Handler, andSocket:Boolean):void {
            if (ConfigApp.releaseWeiXin() && ConfigApp.pf == window.game37_pf && !Tools.isNullString(Platform.pf_login_data["inviterUID"])) {
                pa["invitation"] = Platform.pf_login_data["inviterUID"];
            }
            var _this:* = this;
            NetHttp.instance.send(NetMethodCfg.HTTP_USER_LOGIN, pa, Handler.create(_this, function(re:Object):void {
                if (NetHttp.checkReIsError(re)) {
                    ViewManager.instance.showTipsTxt(re.msg);
                    if (ConfigApp.useMyLogin()) {
                        ViewManager.instance.showView(ConfigClass.VIEW_LOGIN, -1);
                        if (call) {
                            call.runWith([andSocket, true]);
                        }
                    }
                    return;
                }
                _this.http_user_login(re);
                if (call) {
                    call.runWith(andSocket);
                }
            }));
        }

        /**
         * http 登陆返回
         */
        private function http_user_login(re:Object):void {
            Platform.uploadActionData(10007, null);
            if (ConfigApp.isFirstInstall) {
                Platform.uploadActionData(10008, null);
            }
            Platform.checkGameStatus(1500);
            ModelAntiAddiction.instance.age = re.user_age || -1;
            // 
            if (re) {
                member_check = re.member;
                this.isLoginHttp = true;
                // {"uid": 8, "pf_key": null, "last_zone": null, "zones": "", "sessionid": "d2388121adc9d6605e407399888de4be", "pf": ""}
                this.mUID = this.mUID_base = re["uid"] ? re["uid"] : "";
                // 
                this.mSessionid = re["sessionid"] ? re["sessionid"] : "";
                // this.zone = ModelPlayer.instance.getCurrZone();
                this.mUserCode = re["user_code"] ? re["user_code"] : "";
                this.zones_user_num = re["zones_user_num"];
                // this.pay_ip_check = re["is_china"];
                this.accountId = re["user_id"];
                // 
                this.ip = re["user_ip"];
                // 
                this.zone_login = re["zone_login"];
                // 
                ConfigServer.service_info = re["service_info"]; // 服务器维护信息
                ConfigServer.service_zone_list = re["service_zone_list"]; // 服务器维护列表
                // 
                ModelPlayer.instance.setUID(this.mUID_base);
                ModelPlayer.instance.setSessionid(this.mSessionid);
                ModelPlayer.instance.setPlayerCardID(this.mUserCode);
                ModelPlayer.instance.setName(re["username"]);
                ModelPlayer.instance.setPWDs(re["pwd"]);
                // ModelPlayer.instance.setServerZones(re["zones"]);
                ModelPlayer.instance.setServerZonesFormate(this.zone_login, re["zones"]);
                // 
                ModelPlayer.instance.setPlayerList();
                ModelPlayer.instance.tel = re["tel"];
                ModelPlayer.instance.clearReadyTemp();
                // 
                this.http_user_login_data = re;
                // MB (已废弃，改用代金券)
                this.ucoin = re["ucoin"];
                // 生成服务器列表数据
                ConfigServer.getAllZonesData();
                // 
                Platform.uploadActionData(10009, null);
                if (ConfigApp.isFirstInstall) {
                    Platform.uploadActionData(10010, null);
                }
            }
        }

        public function clear_uid_sessionid():void {
            this.mUID = "";
            this.mUID_base = "";
            this.mSessionid = "";
            this.zone = "";
        }

        /**
         * 是否是新地图
         */
        public function get isMergeMaps():Boolean {
            return merge_maps != "";
        }

        /**
         * 设置自己的初始数据
         */
        private function initUserData(re:Object):void {

            if (!this.isLogin) {
                Platform.checkGameStatus(2000);
                // 
                this.data = re;
                if (re.records.writeList)
                    TestUtils.isTestShow = re.records.writeList;
                // 
                NetSocket.instance.registerHandler(NetMethodCfg.WS_SR_SYNC_CONFIG, new Handler(this, this.onConfigChange));
                // 
                for (var key:String in re) {
                    if (this.hasOwnProperty(key)) {
                        this[key] = re[key];
                    }
                }
                if (merge_maps === '-1') {
                    merge_maps = '';
                }
                ConfigServer.modifyMsgDict();
                AssetsManager.modifyResourceVersion();
                if (re.hasOwnProperty("uid")) {
                    this.mUID = re.uid;
                }
                if (re.hasOwnProperty("merge_uid")) {
                    this.mUID_merge = re.merge_uid;
                }
                if (re.hasOwnProperty("head")) {
                    // Trace.log("改头像了");
                }
                if (!re.hasOwnProperty("honour_hero")) {
                    this.honour_hero = {};
                }

                if (re.hasOwnProperty("hero")) {
                    this.checkHeros(this.hero);
                    this.initHeroRelatedData();
                }
                if (re.hasOwnProperty("beast")) {
                    // 必须在initHeroRelatedData调用后初始化空闲兽灵快表
                    ModelBeast.checkBeastSuperData();
                }
                if (re.hasOwnProperty("city_build")) {
                    ModelCityBuild.initCityBuild();
                }
                if (re.hasOwnProperty("visit") || re.hasOwnProperty("estate") || re.hasOwnProperty("city_build")) {
                    setEstateManagerArr();
                }

                if (re.hasOwnProperty("records")) {
                    if (re.records.hasOwnProperty("redbag_num")) {
                        ModelManager.instance.modelClub.u_redbag_num = re.records.redbag_num;
                    }
                }
                if (re.hasOwnProperty("country_club")) {
                    ModelManager.instance.modelClub.updateData(re.country_club);
                }

                if (re.hasOwnProperty("tomb")) {
                    ModelTomb.instance.initData(re["tomb"]);
                }

                if (re.hasOwnProperty("scheme_box")) {
                    ModelScheme.checkSchemeHeroObj();
                }

                if (re.hasOwnProperty("homeland") || re.hasOwnProperty("homeland_building_plan")) {
                    ModelHomeLand.instance.updateData();
                }

                if (re.hasOwnProperty("home")) {
                    ModelArmyUpgrade.updateArmyUpgradeData(re);
                }

                re.auction && ModelAuction.instance.refreshGlobalData(re.auction); // 初始化拍卖数据
                re.legend && ModelLegend.instance.refreshData(re.legend); // 初始化传奇
                re.god && ModelGodMain.instance.refreshData(re.god); // 初始化神灵
                C_NGTask.instance.refreshData(re.ng_task); //新政务

                ModelPlayer.instance.setCurrZone(this.zone);
                ModelPlayer.instance.setZoneList();
                ModelPlayer.instance.setPlayerList();
                // 37h5平台特殊包id判断
                var sd:Object = {uid: this.mUID_base, zone: this.zone, lv: this.getLv()};
                if (ConfigApp.pf == ConfigApp.PF_37_h5) {
                    var c_game_id:String = Platform.get37h5_app_ext();
                    if (c_game_id) {
                        sd["c_game_id"] = c_game_id;
                    }
                }
                NetHttp.instance.send(NetMethodCfg.HTTP_USER_SET_ZONES, sd);
                // 
                this.property = re.prop;
                if (re.hasOwnProperty("prop")) {
                    ModelManager.instance.modelProp.getUserProp(re.prop);
                    ModelSPArmy.getSpArmyByBag();
                }
                this.pub_records = re.pub_records;

                // 
                this.formate_cd_list();
                this.setGameStartTime();
                // 
                ModelUser.checkRuneType();
                ModelUser.checkEquipType();

                // 
                Trace.log(this.getGameSeason(), "-----------自己的数据--------------", re, this);

                ModelManager.instance.modelInside.setArmyCdObj(0);
                ModelManager.instance.modelChat.initLocalMsgTime();
                ModelManager.instance.modelChat.initPKYardNewLocalMsgTime();

                ModelFormation.initFormationObj();
                ModelOffice.setLocalRedPoint();

                ModelChat.initFaceObj();
                ModelChat.initFaceNameObj();
                if (re.hasOwnProperty("chat_cache")) {
                    ModelManager.instance.modelChat.getChatCache(re.chat_cache);
                }
                if (re.hasOwnProperty("peach")) {
                    ModelKunlunPeach.instance.updateData(re);
                }
                FilterManager.instance.setCityBanWord();
                // 
                ThirdRecording.setUid(this.mUID);

                ModelHero.setFestivalHids();
                ModelSalePay.initModels();
                ModelHonour.instance.initHounour();

                re.legend_awaken && ModelLegendAwaken.instance.refreshData(re.legend_awaken); // 初始化传奇觉醒

                // 登录成功后通知跑马灯开始
                ViewManager.instance.event(ViewManager.EVENT_SHOW_CAROUSE);

                ModelSkin.initLocalData();

                ModelChampion.instance.updateData();
                ModelChampion.instance.getMyPkYardHids(null, true);

                ModelManager.instance.modelGame.initRegisterHandler();

                ModelCabinet.instance;
            }
            this.mFunQueue.next();
        }

        private function onConfigChange():void {
            if (ConfigApp.hasDocument) {
                ConfigServer.loadCfgs_socket(mergeZone);
                ConfigServer.modifyMsgDict();
            } else {
                // 服务器js调用（已弃用）
                ConfigServer.loadCfgs_old();
            }

        }

        /**
         * 游戏启动后,在主场景之前的功能画面
         */
        public function initSupporUI():void {
            if (!this.isLogin) {
                if (ConfigApp.testFightType == 2) {
                    // if(ConfigApp.isTest){
                    TestCopyright.sendInit();
                    TestCopyrightData.init();
                    // }
                    ViewManager.instance.initCheckTired();

                    ViewManager.instance.closeScenes(true);

                    FightMain.startCountryBattle(null);
                    // ViewManager.instance.showTipsTxt(Tools.getMsgById("pupil1"),5);
                    return;
                }
                // 
                ModelManager.instance.modelGame.checkRealNameTimeTips(this.online_time); // 实名检测机制提醒
                ViewManager.instance.closeScenes(true);
                if (ModelUser.getCountryID() > -1) {
                    this.initModels(1);
                } else {
                    // 国家选择
                    LoadeManager.instance.event(LoadeManager.REMOVE_SELF);
                    if (HelpConfig.type_app == HelpConfig.TYPE_WW) {
                        ViewManager.instance.showView(ConfigClass.VIEW_COUNTRY_WW, Handler.create(this, this.initModels), {type: 0});
                    } else {
                        if (this.isMergeMaps) {
                            // 新地图
                            ViewManager.instance.showView(ConfigClass.VIEW_COUNTRY_MAP0, Handler.create(this, this.initModels), {type: 0});
                        } else {
                            if (ConfigApp.isHorizontal) {
                                ViewManager.instance.showView(ConfigClass.VIEW_COUNTRY_PC, Handler.create(this, this.initModels), {type: 0});
                            } else {
                                ViewManager.instance.showView(ConfigClass.VIEW_COUNTRY, Handler.create(this, this.initModels), {type: 0});
                            }
                        }

                    }

                }
            }
        }

        /**
         * 各功能固定 model 初始化/数据处理/功能数据整理
         */
        public function initModels(type:Number = 0):void {
            Platform.uploadUserData(1, [type]);
            // Trackingio.postReport(4, {uid: this.mUID});
            // 
            LoadeManager.instance.showLoadPanel(0);
            // 初始化引导模型
            GuideChecker.instance.initGuide();
            // 
            ModelManager.instance.modelInside.initCDlistener(); // 初始化建筑数据,和农民一起初始化
            // 
            ModelManager.instance.modelProp.getPveGiftDict();
            // 初始化各个活动模型
            ModelActivities.instance.initModel();
            // 初始化任务模型
            TaskHelper.instance.initTaskModel();
            // 初始化跨服战
            ModelDuplicate.instance;
            // 军团
            // ModelManager.instance.modelGame.getMyguildData();
            // 地图数据
            var _this:* = this;
            // 
            checkSaveLocal();

            loadOtherAssets();

            MapModel.instance.initLoadMap(new Handler(_this, function():void {
                LoadeManager.instance.onProgress(getInitLoadPer());
                Laya.timer.once(100, _this, _this.initTroopModel);
            }));
        }

        /**
         * 根据条件加载素材
         * 一进游戏就要用到的素材
         */
        public function loadOtherAssets():void {
            if (ConfigApp.isLandscape) {
                var arr:Array = [];
                if (QueueManager.instance.showGetWeapen() != "") {
                    arr.push(AssetsManager.getAssetLater("bg_jy_1.png"));
                    arr.push(AssetsManager.getAssetLater("bg_jy_2.png"));
                }

                if (arr.length > 0)
                    LoadeManager.loadImg(arr);
            }
        }

        /**
         * 检查修改本地缓存
         */
        private function checkSaveLocal():void {
            var data:Object = SaveLocal.getValue(SaveLocal.KEY_AFFICHE + mUID_base) || {days: 0, mergeNum: 0};
            if (data.days < getGameDate() || data.mergeNum < mergeNum) {
                data.days = Math.max(data.days, getGameDate());
                data.mergeNum = Math.max(data.mergeNum, mergeNum);
                SaveLocal.save(SaveLocal.KEY_AFFICHE + mUID_base, data);
            }
        }

        /**
         * 初始化 部队信息
         */
        private function initTroopModel():void {
            LoadeManager.instance.onProgress(getInitLoadPer());
            var _this:* = this;
            ModelManager.instance.modelTroopManager.init(Handler.create(_this, function():void {
                LoadeManager.instance.onProgress(getInitLoadPer());
                Laya.timer.once(100, _this, _this.initMainUI);
            }));
        }

        /**
         * 主界面 初始化 UI
         */
        public function initMainUI():void {
            this.isLogin = true; // 登录成功
            this.loadWaitToEnd();
        }

        /**
         * 强制 等待 初始各自数据
         */
        private function loadWaitToEnd():void {
            Laya.timer.clear(this, this.loadWaitToEnd);
            if (sInitLoadMax < 1 || !ModelOfficial.countries) {
                LoadeManager.instance.onProgress(getInitLoadPer());
                Laya.timer.once(50, this, this.loadWaitToEnd);
            } else {
                LoadeManager.instance.onComplete();
                // 游戏UI初始化
                ViewManager.instance.initGame();
                // 需要检测防沉迷
                if ((ConfigServer.system_simple.indulge && ConfigServer.system_simple.indulge.pfs && ConfigServer.system_simple.indulge.pfs.indexOf(ConfigApp.pf) != -1)) {
                    if (ModelAntiAddiction.instance.age >= ConfigServer.system_simple.indulge["indulge_age_tips"]) {
                        this.initQueue();
                    } else {
                        ViewManager.instance.showView(["ViewAgeNotice", ViewAgeNotice], [Tools.getMsgById("indulge_title"), Tools.getMsgById("indulge_notice"), Handler.create(this, this.initQueue)]);
                    }
                } else {
                    this.initQueue();
                }
            }
        }

        /**
         * 游戏启动后,各种功能显示
         */
        public function initQueue():void {
            Trace.log("------游戏启动后,各种功能显示-------");

            // 答题活动开始
            ModelFestivalAsk.instance.showStartTips();

            Platform.obtainCheck(); // 充值补单检查
            QueueManager.instance.checkInitQueue();
            QueueManager.instance.showFirst();

            // 初始化成就
            ModelAchievement.instance.initModelAchievement();

            // 引导开始
            GuideChecker.instance.startGuide();
        }

        /**
         * 更新已经改变的数据
         */
        public function updateData(re:*):void {
            if (Tools.isNullObj(re)) {
                return;
            }
            if (Tools.isNullObj(this.data)) {
                return;
            }
            var userRe:Object = {};
            if (re.hasOwnProperty("user")) {
                userRe = re.user;
            }
            if (Tools.isNullObj(userRe)) {
                return;
            }
            // 老的官邸等级
            var oldLv:int = this.getLv();

            var changedData:Object = this.checkPayUpdate(userRe);
            changedData && event(EVENT_TOP_UPDATE, [changedData]);

            this.checkBuyWeapon(userRe); // 检查是否有购买武器活动
            var oldCoin:Number = this.coin;
            var oldGold:Number = this.gold;
            // 
            var changeShogunValue:Boolean;
            if (userRe.hasOwnProperty("shogun_value")) {
                // 幕府效果值与当前不同，强制刷新所有战力
                if (!FightUtils.compareObj(this.shogun_value, userRe.shogun_value, null)) {
                    changeShogunValue = true;
                }
            }

            var oldYearCredit:int = this.year_credit;

            var partPropertys:Array = ['hero', 'records', 'honour_hero', 'homeland', 'homeland_building_plan', 'gwent', 'god', 'puppet', 'puppet_arm'];
            for (var key:String in userRe) {
                // 需要局部刷新的字段 英雄、赛季等 单独刷新 不全覆盖
                // var partPropertys:Array = ['hero', 'records', 'honour_hero', 'homeland', 'homeland_building_plan', 'gwent'];
                if (partPropertys.indexOf(key) !== -1) {
                    if (this[key] === undefined) { this[key] = {}; }
                    for (var key2:String in userRe[key]) {
                        this.data[key][key2] = this[key][key2] = userRe[key][key2];
                    }
                } else {
                    this.data[key] = userRe[key];
                    if (this.hasOwnProperty(key)) {
                        this[key] = userRe[key];
                    }
                }
            }
            // 新官邸等级
            var newLv:int = this.getLv();
            if (newLv > oldLv) {
                Trace.log("官邸升级");
                ModelManager.instance.modelInside.checkBuildingLvUp();
                this.event(EVENT_USER_LV_UP);
            }

            var newCoin:Number = userRe.hasOwnProperty("coin") ? userRe["coin"] : -1;
            if (newCoin >= 0 && oldCoin != newCoin) {
                Platform.uploadUserData(4, [oldCoin, newCoin]);
            }

            var newGold:Number = userRe.hasOwnProperty("gold") ? userRe["gold"] : -1;
            if (newGold >= 0 && oldGold != newGold) {
                Platform.uploadUserData(6, [oldGold, newGold]);
            }

            var newYearCredit:int = this.year_credit;
            if (newYearCredit > oldYearCredit) {
                this.event(ModelUser.EVENT_UPDATE_CREDIT);
            }

            if (re.hasOwnProperty("country_club")) {
                ModelManager.instance.modelClub.updateData(re.country_club);
            }

            if (re.hasOwnProperty("country_data")) {
                // 国家上的数据
                var myCountry:Object = ModelOfficial.countries[ModelManager.instance.modelUser.country];
                var reCountry:Object = re["country_data"];
                for (var s:String in reCountry) {
                    if (myCountry.hasOwnProperty(s)) {
                        myCountry[s] = reCountry[s];
                    }
                }
            }

            if (userRe.hasOwnProperty("alien_reward")) {
                // 异邦来访获得的盒子
                ModelManager.instance.modelClub.event(ModelClub.EVENT_COUNTRY_ALIEN_RED);
            }
            if (userRe.hasOwnProperty("hero")) {
                // 更新副将相关数据
                this.checkHeros(this.hero);
            }
            if (userRe.hasOwnProperty("beast")) {
                ModelBeast.checkBeastSuperData();
            }

            var updateShogun:Boolean = false;

            // 全部item
            if (userRe.hasOwnProperty("prop")) {

                var shogunItemArr:Array = ConfigServer.shogun.shogun_book;
                var shogunItemNum1:int = 0;
                var shogunIndex:int = 0;
                var shogunItemId:String = "";
                for (shogunIndex = 0; shogunIndex < shogunItemArr.length; shogunIndex++) {
                    shogunItemId = shogunItemArr[shogunIndex];
                    shogunItemNum1 += (this.property[shogunItemId] ? this.property[shogunItemId] : 0);
                }

                // 更新道具
                this.property = userRe.prop;
                ModelManager.instance.modelProp.getUserProp(userRe.prop);
                ModelManager.instance.modelProp.setLocalRedPointShow();

                var shogunItemNum2:int = 0;
                for (shogunIndex = 0; shogunIndex < shogunItemArr.length; shogunIndex++) {
                    shogunItemId = shogunItemArr[shogunIndex];
                    shogunItemNum2 += (this.property[shogunItemId] ? this.property[shogunItemId] : 0);
                }

                if (shogunItemNum1 != shogunItemNum2) {
                    // 通知官邸刷新
                    ModelManager.instance.modelInside.updateBaseBuilding();
                    updateShogun = true;
                }

            }

            if (updateShogun == false && userRe.hasOwnProperty("shogun")) {
                ModelManager.instance.modelInside.updateBaseBuilding();
            }

            if (!re["methodName"]) {
                // 发送聊天消息的时候也调用这个，感觉没必要。methodName目前只可能=chat
                // 检查各种数据记录
                this.checkRecords(userRe);
            }

            // 检查关注认证
            if (userRe.hasOwnProperty("records_360")) {
                ModelActivities.instance.refreshLeftList();
            }

            // 建筑升级cd
            if (userRe.hasOwnProperty("building_cd")) {
                this.formate_cd_list();
            }

            // 爵位
            if (userRe.hasOwnProperty("office")) {
                event(EventConstant.OFFICE);
            }
            // 变法
            if (userRe.hasOwnProperty("office37")) {
                event(EventConstant.OFFICE37);
            }

            // 切磋
            if (userRe.hasOwnProperty("hero_catch") && ModelManager.instance.modelUser.getUseFunctionByKey("use_drink") != 1) {
                event(EventConstant.HERE_CATCH, Tools.getFullHourDis());
                this.event(ModelUser.EVENT_UPDATE_BTM_BTN);
            }
            // 酒宴
            if (userRe.hasOwnProperty("drink") && ModelManager.instance.modelUser.getUseFunctionByKey("use_drink") == 1) {
                event(EventConstant.HERO_DRINK);
                this.event(ModelUser.EVENT_UPDATE_BTM_BTN);
            }

            if (userRe.hasOwnProperty("visit") || userRe.hasOwnProperty("estate") || userRe.hasOwnProperty("city_build")) {
                setEstateManagerArr();
                this.event(ModelUser.EVENT_UPDATE_BTM_BTN);
            }

            if (userRe.hasOwnProperty("baggage")) {
                if (ModelManager.instance.modelGame.inHome) {
                    ModelManager.instance.modelInside.getBuildingModel(ModelBuiding.getBaggageId()).updateStatus();
                }
            }
            // 任务
            if (userRe.hasOwnProperty("task")) {
                TaskHelper.instance.onTaskProgressChange(userRe['task']);
            }
            if (userRe.hasOwnProperty("gtask")) {
                TaskHelper.instance.event(TaskHelper.REFRESH_TASK_STORY);
            }
            // 成就
            if (userRe.hasOwnProperty("effort")) {
                ModelAchievement.instance.refreshData(userRe['effort']);
            }
            // 天下大势面板
            if (userRe.hasOwnProperty("milepost_reward") || userRe.hasOwnProperty("milepost_fight_reward")) {
                ModelManager.instance.modelGame.event(ModelOfficial.EVENT_UPDATE_ORDER_ICON);
            }
            // 探险
            if (userRe.hasOwnProperty("mining") || false) {
                ModelExplore.instance.refreshData(userRe);
            }
            // 拍卖
            if (userRe.hasOwnProperty("auction")) {
                ModelAuction.instance.refreshGlobalData(userRe.auction);
            }
            // 传奇
            if (userRe.hasOwnProperty("legend")) {
                ModelLegend.instance.refreshData(userRe.legend);
            }
            // 福将挑战
            if (userRe.hasOwnProperty("bless_hero")) {
                ModelBlessHero.instance.refreshData(userRe.bless_hero);
            }
            // 传奇觉醒
            if (userRe.hasOwnProperty("legend_awaken")) {
                ModelLegendAwaken.instance.refreshData(userRe.legend_awaken);
            }
            // 跨服战
            if (userRe.hasOwnProperty("duplicate_records")) {
                ModelDuplicate.instance.refreshData(userRe.duplicate_records);
            }
            // 异族入侵
            if (userRe.hasOwnProperty("pk_npc")) {
                ModelManager.instance.modelGame.checkPKnpcFightTimerStatus();
            }
            if (userRe.hasOwnProperty("star")) {
                ModelUser.checkRuneType();
            }
            if (userRe.hasOwnProperty("equip")) {
                ModelUser.checkEquipType();
            }
            if (userRe.hasOwnProperty("levelup_gift")) {
                LevelupGiftModelManager.instance.refreshData(userRe.levelup_gift);
            }
            if (userRe.hasOwnProperty("tomb")) {
                ModelTomb.instance.updateData(userRe["tomb"]);
            }
            if (userRe.hasOwnProperty("surprise_gift_new")) {
                ModelSurpriseGiftNew.instance.updateNew(userRe["surprise_gift_new"]);
            }
            if (userRe.hasOwnProperty("surprise_gift_day")) {
                ModelSurpriseGiftNew.instance.updateDay(userRe["surprise_gift_day"]);
            }
            if (userRe.hasOwnProperty("surprise_gift_ever")) {
                ModelSurpriseGiftNew.instance.updateEver(userRe["surprise_gift_ever"]);
            }
            if (userRe.hasOwnProperty("ng_task")) {
                C_NGTask.instance.refreshData(userRe['ng_task']); //新政务
            }

            if (userRe.hasOwnProperty("msg")) {
                ModelManager.instance.modelChat.setChatData(userRe["msg"]["usr"]);
                    // ModelManager.instance.modelUser.event(ModelUser.EVENT_UPDATE_MAIL_CHAT_MAIN,userRe["msg"]["usr"]);
            }

            if (userRe.hasOwnProperty("scheme_box")) {
                ModelScheme.checkSchemeHeroObj();
            }

            if (userRe.hasOwnProperty("homeland") || userRe.hasOwnProperty("homeland_building_plan")) {
                ModelHomeLand.instance.updateData();
            }

            if (userRe.hasOwnProperty("home")) {
                ModelArmyUpgrade.updateArmyUpgradeData(userRe);
            }

            if (userRe.hasOwnProperty("invitation")) {
                ModelWXShare.instance.redPoint2();
            }

            if (userRe.hasOwnProperty("god")) {
                ModelGodMain.instance.refreshData(userRe.god);
            }

            if (userRe.hasOwnProperty("peach")) {
                ModelKunlunPeach.instance.updateData(userRe);
            }

            // 
            Laya.timer.frameOnce(1, this, this.checkProIsChangePower, [userRe, changeShogunValue]);
            // 
            userDataProxy.checkUserData(userRe); // 等待别的数据先刷新 eg: prop
            this.event(EVENT_USER_UPDATE, re);
            // var ns:Number = Tools.getGameDayDeviationTime(ConfigServer.getServerTimer()); // 当前时间
            // var st:Number = ns;
            // var et:Number = ns + Tools.oneDayMilli;
            // Trace.log("现在时间：",Tools.dateFormat(ConfigServer.getServerTimer()),"新的一天: ",Tools.dateFormat(et));
        }

        private var userDataProxy:UserDataProxy = new UserDataProxy();
        /**
         * 注册数据监听事件，当用户数据更新时通知
         * @param listenField 需要监听的用户数据名称 eg: 'field' | 'field1.field2' | 'field1.2.field2'
         */
        public function onUserUpdate(listenField:String, caller:*, listener:Function, args:Array = null):void {
            userDataProxy.on(listenField, caller, listener, args);
        }

        /** 移除用户数据监听事件 */
        public function offUserUpdate(listenField:String, caller:*, listener:Function, onceOnly:Boolean = false):void {
            userDataProxy.off(listenField, caller, listener, onceOnly);
        }

        public static function checkRuneType():void {
            var id:String = "";
            // rune_type_dic_sp = {};
            rune_type_dic = {};
            for (var key:String in ModelManager.instance.modelUser.star) {
                id = key.split("|")[0];
                var fixType:int = ConfigServer.star[id].fix_type;
                if (!rune_type_dic[fixType]) {
                    rune_type_dic[fixType] = {};
                }
                rune_type_dic[fixType][key] = ModelManager.instance.modelUser.star[key];

                    // if (ModelRune.isOnly(id)) {
                    // var hid:String = ModelManager.instance.modelUser.star[key].hid;
                    // if (hid) {
                    // if (!rune_type_dic_sp.hasOwnProperty(id)) {
                    // rune_type_dic_sp[id] = [];
                    // }
                    // if (rune_type_dic_sp[id].indexOf(hid) < 0) {
                    // rune_type_dic_sp[id].push(hid);
                    // }
                    // }
                    // }
            }
        }

        public static function checkEquipType():void {
            var id:String = "";
            for (var key:String in ModelManager.instance.modelUser.equip) {
                // if(!ModelEquip.searchHero(key)){
                if (!equip_type_dic[ConfigServer.equip[key].type]) {
                    equip_type_dic[ConfigServer.equip[key].type] = {};
                    equip_type_dic[ConfigServer.equip[key].type][key] = ModelManager.instance.modelUser.equip[key];
                } else {
                    equip_type_dic[ConfigServer.equip[key].type][key] = ModelManager.instance.modelUser.equip[key];
                }
                    // }
            }
        }

        /**
         * solo战斗刷新部队
         */
        public function soloFightUpdateTroop(re:*):void {
            if (re.hasOwnProperty("pk_result")) {
                var obj:Object = re.pk_result.userTroop;
                var hid:String = "";
                var d:Object = {};
                for (var s:String in obj) {
                    hid = s;
                    d["army"] = obj[s];
                    ModelManager.instance.modelTroopManager.setTroopData(EventConstant.TROOP_UPDATE, hid, d);
                }
            }
        }

        /**
         * 检测 那些 属性变化 影响 总战力
         */
        private var myLastPowerNum:Number = -1;

        private function checkProIsChangePower(ud:Object, mustUpdate:Boolean = false):void {
            // 哪些字段更新后需要重算战力
            var powerKeys:Array = ['hero',
                'science',
                'equip',
                'star',
                'home',
                'beast',
                'doom_tower_exp', // 王之魂
                'doom_type_exp', // 分魂
                'god', // 神灵
                'puppet', // 傀儡
                'puppet_arm', // 傀儡武装
                ];
            var needUpdate:Boolean = powerKeys.some(function(key:String):Boolean {
                return ud.hasOwnProperty(key);
            });
            // 缺少 国王 称帝 的判断
            if (needUpdate || mustUpdate) {
                var p:Number = this.getPower(true);
                if (TestUtils.isTestShow) {
                    trace('getPower', p);
                }
                if (p != myLastPowerNum) {
                    myLastPowerNum = p;
                    Platform.uploadUserData(7, [myLastPowerNum]); // 上报战力变化
                }
            }
        }

        private function checkPayUpdate(userRe:Object):Object {
            var result:Object = null;
            var resIds:Array = ["gold", "food", "wood", "iron", "coin", "merit"];
            var user:ModelUser = this;
            // 此处只检查消耗，获得的通过giftdict检查
            resIds.forEach(function(key:String):void {
                if (userRe[key] != null && userRe[key] < user[key]) {
                    result ||= {};
                    result[key] = userRe[key] - user[key];
                }
            });
            return result;
        }

        public static const CHECK_RECORDS:String = 'check_records';

        private function checkRecords(userRe:Object):void {
            var records:* = userRe.records;
            if (records) {
                ModelActivities.instance.refreshActivitiesData(records);
                records.pay_gtask_reward && this.checkGtaskReward(records.pay_gtask_reward);
                if (records.hasOwnProperty("redbag_num")) {
                    ModelManager.instance.modelClub.u_redbag_num = records.redbag_num;
                }
                if (records.hasOwnProperty("idol_login")) {
                    ModelManager.instance.modelIdolLogin.checkData(records.idol_login);
                }
                records.freeze && this.checkFreeze(records.freeze);
            }
            userRe.member && ModelMemberCard.instance.refreshData(userRe.member);
            userRe.optional_ones && ModelOptionalOnes.instance.refreshData(userRe.optional_ones);
            userRe.big_shot && ModelBigShot.instance.refreshData(userRe.big_shot);
            userRe.loop_card && ModelLoopCard.instance.refreshData(userRe.loop_card);
            event(CHECK_RECORDS);
        }

        private function checkFreeze(freeze:Object):void {
            var now:int = ConfigServer.getServerTimer();
            var endTime:int = 0;
            if (freeze[1]) {
                endTime = Tools.getTimeStamp(freeze[1]);
            }
            if (freeze[0] === 1 && now < endTime) {
                Platform.restart();
            }
        }

        public function get canTalk():Boolean {
            var freeze:Object = records.freeze;
            var now:int = ConfigServer.getServerTimer();
            var endTime:int = 0;
            if (freeze && freeze[1]) {
                endTime = Tools.getTimeStamp(freeze[1]);
            }
            if (freeze && freeze[0] === 2 && now < endTime) {
                ViewManager.instance.showTipsTxt(Tools.getMsgById('_jia0145'));
                return false;
            }
            return true;
        }

        /**
         * 初始化英雄相关数据
         * initUser的时候调用一次
         * (目前只有兽灵和奇士)
         */
        private function initHeroRelatedData():void {
            for (var h:String in this.hero) {
                if (this.hero[h].sp_id) {
                    var sp:Array = this.hero[h].sp_id;
                    for (var i:int = 0; i < sp.length; i++) {
                        if (sp[i]) {
                            ModelSPArmy.getModel(sp[i]).hid = h;
                        }
                    }
                }

                if (this.hero[h].beast_ids) {
                    var beast:Array = this.hero[h].beast_ids;
                    for (var j:int = 0; j < beast.length; j++) {
                        if (beast[j]) {
                            ModelBeast.getModel(Number(beast[j])).hid = h;
                        }
                    }
                }
            }
        }

        private function checkHeros(hero:Object):void {
            var currentTime:int = ConfigServer.getServerTimer();
            Laya.timer.clear(this, titleExpired);
            for (var hid:String in hero) {
                var tempHero:Object = hero[hid];

                // 记录拥有的副将
                var adjutant:Array = tempHero['adjutant'];
                if (adjutant) {
                    tempHero['isCommander'] = false; // 是否是主将
                    if (adjutant[0]) {
                        hero[adjutant[0]]['commander'] = hid; // 设置主将
                        tempHero['isCommander'] = true;
                    }
                    if (adjutant[1]) {
                        hero[adjutant[1]]['commander'] = hid; // 设置主将
                        tempHero['isCommander'] = true;
                    }
                } else {
                    tempHero['isCommander'] = false;
                    tempHero['adjutant'] = [null, null];
                }
                tempHero['commander'] || (tempHero['commander'] = '');

                // 检测没过期的称号
                var title:Array = tempHero.title;
                if (title) {
                    var expireTime:int = Tools.getTimeStamp(title[1]);
                    var titleIsOK:Boolean = ModelHero.checkTitleIsOK(expireTime);
                    if (titleIsOK) {
                        Laya.timer.once(expireTime - currentTime, this, titleExpired, [hid, title[0]], false);
                    }
                }
            }

            // 兽灵处理
            // if(this.beast){
            // for(var beastId:String in this.beast){
            // ModelBeast.getModel(Number(beastId)).hid = "";
            // for(var heroId:String in this.hero){
            // if(this.hero[heroId].beast_ids){
            // for(var i:int=0;i<this.hero[heroId].beast_ids.length;i++){
            // if(this.hero[heroId].beast_ids[i] && this.hero[heroId].beast_ids[i]==beastId){
            // ModelBeast.getModel(Number(beastId)).hid = heroId;
            // }
            // }
            // }
            // }
            // }
            // }
        }

        private function titleExpired(hid:String, tid:String):void {
            NetSocket.instance.send('notice_expired_title', {hid: hid}); // 通知后端称号掉了
            hero[hid].title = null;
            // 
            // var heroName:String = ModelHero.getHeroName(hid, hero[hid].awaken);
            var powerTemp:Number = myPowerTemp;
            var power:Number = getPower(true);
            if (power !== powerTemp) {
                UserPowerChange.titleName = ModelHero.getTitleName(tid);
                UserPowerChange.tips = Tools.getMsgById('_jia0188');
            }
        }

        /**
         * 检查福利兑换
         */
        public function checkFreeBuy(re:Object):void {
            var user:Object = re["user"];
            if (user && user.hasOwnProperty("records")) {
                var free_buy:Object = user["records"].free_buy;
                if (free_buy) {
                    for (var s:String in free_buy) {
                        if (free_buy[s] != null && this.records.free_buy[s] == null) {
                            free_buy_key = free_buy[s][0] + "_" + s;
                            break;
                        }
                    }
                }
            }

        }

        /**
         * 检查是否有“购买武器”活动
         */
        public function checkBuyWeapon(re:Object):void {
            if (re && re.hasOwnProperty("records")) {
                var buy_weapon:Object = re["records"].buy_weapon;
                if (buy_weapon) {
                    var user:Object = this.records["buy_weapon"];
                    for (var s:String in buy_weapon) {
                        if (user == null || !user.hasOwnProperty(s)) {
                            free_buy_key = "buy_weapon_" + s;
                            break;
                        }
                    }
                    this.records["buy_weapon"] = re["records"].buy_weapon;
                    ModelFreeBuy.instance.addData();
                }

            }
        }

        /**
         * num 需要消耗的量
         */
        public function checkPayMoneyAddArmy(num:int):Boolean {
            var system_simple:Object = ConfigServer.system_simple;
            var cost_type:String = system_simple.fast_train_type;
            return this[cost_type] >= num && pay_money >= system_simple.fast_train_pay && ModelGame.unlock(null, 'fast_train').visible;
        }

        /**
         * 凤雏理政 充钱送居功至伟次数
         */
        private function checkGtaskReward(pay_gtask_reward:Array):void {
            if (pay_gtask_reward[2] === 0 && pay_gtask_reward[3] === 0 && Tools.getRemainTime(pay_gtask_reward[1]) > 0) {
                if (ModelFreeBuy.instance.gtaskTime !== Tools.getTimeStamp(pay_gtask_reward[1])) {
                    ModelFreeBuy.instance.gtaskTime = Tools.getTimeStamp(pay_gtask_reward[1]);
                    free_buy_key = 'gtask';
                }
            }
        }

        public function formate_cd_list():void {
            this.building_cd_arr = [];
            for (var key:String in this.building_cd) {
                this.building_cd_arr.push({id: key, cd: Tools.getTimeStamp(this.building_cd[key])});
            }
        }

        public function getLocalCountry():void {

        }

        /**
         * 官邸等级
         */
        public function getLv():Number {
            return ModelManager.instance.modelInside.getBase().lv;
        }

        /**
         * 称号集
         */
        public function getMyTitle():Array {
            if (this.records.hasOwnProperty("title")) {
                return this.records["title"];
            }
            return [];
        }

        public function getMyTitleCanSet():Boolean {
            var b:Boolean = false;
            var noUsed:Array = this.getMyTitle();
            var len:int = noUsed.length;
            var endMs:Number = 0;
            var now:Number = ConfigServer.getServerTimer();
            for (var i:int = 0; i < len; i++) {
                endMs = Tools.getTimeStamp(noUsed[i][1]);
                if (endMs > now) {
                    b = true;
                    break;
                }
            }
            return b;
        }

        /**
         * 我的所有称号排序
         */
        public function getMyTitleAll():Array {
            var noUsed:Array = this.getMyTitle();
            var heros:Array = [];
            var endMs:Number = 0;
            var now:Number = ConfigServer.getServerTimer();
            var _sort1:Number = 0; // 是否安装
            var _sort2:Number = 0; // 品质  修改为index
            var _sort3:Number = 0; // id
            var tid:String = "";
            var titleArr:Array;
            for (var key:String in this.hero) {
                if (this.hero[key]) {
                    titleArr = this.hero[key]["title"];
                    if (titleArr) {
                        endMs = Tools.getTimeStamp(titleArr[1]);
                        if (endMs > now) {
                            heros.push(ModelTitle.getSortObject(titleArr, -1, key));
                                // heros.push({data:this.hero[key]["title"],hid:key,index:-1,sort1:_sort1,sort2:_sort2,sort3:_sort3});
                        }
                    }
                }
            }
            var len:int = noUsed.length;
            for (var i:int = 0; i < len; i++) {
                titleArr = noUsed[i];
                endMs = Tools.getTimeStamp(titleArr[1]);
                if (endMs > now) {
                    heros.push(ModelTitle.getSortObject(titleArr, i));
                        // heros.push({data:noUsed[i],hid:"",index:i,sort1:_sort1,sort2:_sort2,sort3:_sort3});
                }
            }
            heros = ArrayUtils.sortOn(["sort1", "sort2", "sort3"], heros, true);
            return heros;
        }

        /**
         * 获得 玩家 已有星辰 配置,先格式化 id
         * starType 0 星辰 1 神纹
         */
        public function getStarList(starType:int):Array {
            var o:Array = [];
            var o1:Array = [];
            var o2:Array = [];
            var configData:Object = ConfigServer.star;
            for (var s:String in this.star) {
                var itemStar:Object = {};
                var cid:String = s;
                var temp:int = s.indexOf("|");
                if (temp > -1) {
                    cid = s.substring(0, temp);
                }

                if (star[s].hid == null && configData.hasOwnProperty(cid)) {
                    if (starType == 0 && configData[cid].fix_type >= 4)
                        continue;

                    if (starType == 1 && configData[cid].fix_type < 4)
                        continue;

                    var itemRune:ModelRune = new ModelRune();
                    itemRune.initData(cid, configData[cid]);
                    itemStar["id"] = cid;
                    itemStar["cid"] = s;
                    itemStar["fix_type"] = itemRune.fix_type;
                    itemStar["exp_type"] = itemRune.exp_type;
                    itemStar["name"] = itemRune.name;
                    itemStar["lv"] = star[s].lv;
                    itemStar["exp"] = star[s].exp;
                    itemStar["index"] = itemRune.index;
                    itemStar["icon"] = itemRune.getImgName();

                    itemStar["sortLv"] = -(star[s].lv);
                    itemStar["sortExp"] = -(star[s].exp);
                    itemStar["sortIndex"] = -itemRune.index;
                    o.push(itemStar);
                }
            }
            ArrayUtils.sortOn(["fix_type", "sortIndex", "sortLv", "sortExp"], o, false);
            return o;
        }

        /**
         * 获取 可以 编辑 部队 的英雄 id 数组
         */
        public function getTroops():Array {
            var troopAll:Object = ModelManager.instance.modelTroopManager.troops;
            // this.uid + "&" + this.hero
            var tid:String = "";
            var arr:Array = [];
            var hmd:ModelHero;
            for (var key:String in hero) {
                tid = this.mUID + "&" + key;
                if (!troopAll.hasOwnProperty(tid)) {
                    hmd = ModelManager.instance.modelGame.getModelHero(key);

                    if (this.getCommander(hmd.id)) {
                        continue;
                    }
                    hmd["sortPower"] = hmd.getPower();
                    arr.push(hmd);
                }
            }
            return arr;
        }

        /**
         * 查看 任何 用户信息,统一的ui
         */
        public function selectUserInfo(uid:*):void {
            uid = Number(uid);
            if (uid < 0)
                return;
            NetSocket.instance.send(NetMethodCfg.WS_SR_USER_INFO, {uid: uid}, Handler.create(this, this.ws_sr_user_info, [uid]));
        }

        /**
         * 查看 任何 用户信息的指定英雄
         * 如果没有zone值则传空
         */
        public function selectHeroInfo(uid:*, hid:String, zone:String = ""):void {
            uid = Number(uid);
            if (uid < 0)
                return;
            NetSocket.instance.send(NetMethodCfg.WS_SR_USER_INFO, {uid: uid, zone: zone}, Handler.create(this, this.ws_sr_hero_info, [uid, hid]));
        }

        /**
         * 调接口刷新用户数据
         */
        public function checkUserData(arr:Array, fun:Function = null):void {
            var this2:* = this;
            if (arr == null || arr.length == 0) {
                return;
            }
            NetSocket.instance.send("update_user", {"key": arr}, new Handler(this, function(np:NetPackage):void {
                updateData(np.receiveData);
                if (fun) {
                    if (fun is Handler) {
                        (fun as Handler).run();
                    } else {
                        var handler:Handler = Handler.create(this2, fun);
                        handler && handler.run();
                    }
                }
            }));
        }

        /**
         * 查看玩家在线状态
         */
        public function checkUserOnline(uids:Array, fun:* = null):void {
            var this2:* = this;
            NetSocket.instance.send("get_online", {"uids": uids}, new Handler(this, function(np:NetPackage):void {
                if (fun) {
                    if (fun is Handler) {
                        (fun as Handler).runWith(np.receiveData);
                    } else {
                        var handler:Handler = Handler.create(this2, fun, [np.receiveData]);
                        handler && handler.runWith(np.receiveData);
                    }
                }
            }));
        }

        /**
         * 打开返回的玩家信息面板
         */
        private function ws_sr_user_info(uid:String, re:NetPackage):void {
            re.receiveData["id"] = uid;
            ViewManager.instance.showView(ConfigClass.VIEW_USER_INFO, re.receiveData);
        }

        /**
         * 打开返回的英雄信息面板
         */
        private function ws_sr_hero_info(uid:String, hid:String, re:NetPackage):void {
            var reData:Object = re.receiveData;
            reData["id"] = uid;
            if (reData.hero && reData.hero[hid]) {
                var heroData:Object = reData.hero[hid];
                if (heroData) {
                    ViewManager.instance.showView(ConfigClass.VIEW_HERO_INFO, heroData);
                    return;
                }
            }
            // 返回的前十英雄里没有该英雄，打开玩家信息
            ViewManager.instance.showView(ConfigClass.VIEW_USER_INFO, reData);
        }

        /**
         * 获取给定英雄的主将（可用来判断是否是副将）
         * @param id 英雄id
         *
         */
        public function getCommander(id:String):String {
            if (hero[id]) {
                return hero[id]['commander'];
            }
            return '';
        }

        /**
         * 获取 自己 当前 全部已有英雄的 战力排序
         * @param	filterAdjutant 是否过滤掉副将  默认不过滤
         * @param	duplicateLevel 跨服赛段位。指定后使用跨服模式，排除官职、称号、赛季等级，并限制在level_limit配置中，该段位对应的键值
         */
        public function getMyHeroArr(sortPower:Boolean = false, removeId:String = "", ext:Array = null, filterAdjutant:Boolean = false, duplicateLevel:int = -1):Array {
            var arr:Array = [];
            var hmd:ModelHero;
            for (var key:String in hero) {
                if (filterAdjutant && this.getCommander(key)) {
                    continue;
                }

                hmd = ModelManager.instance.modelGame.getModelHero(key);
                if (duplicateLevel != -1) {
                    var tempHmd:ModelHero = new ModelHero(true);
                    tempHmd.setData(hmd.id);
                    tempHmd.getPrepare(true, hmd.getPrepareObjBy(-1, -1, null, duplicateLevel));
                    hmd = tempHmd;
                }
                if (sortPower) {
                    hmd["sortPower"] = hmd.getPower();
                }
                if (hmd.id != removeId) {
                    if (ext) {
                        if (ext.indexOf(hmd.id) < 0) {
                            arr.push(hmd);
                        }
                    } else {
                        arr.push(hmd);
                    }

                }
            }
            if (sortPower && arr.length > 1) {
                arr.sort(MathUtil.sortByKey("sortPower", true));
            }
            return arr;
        }

        /**
         * 设置 游戏时间
         */
        public function setGameStartTime():void {
            this.gameServerStartTimer = Tools.getGameServerStartTimer(this.mergeZone);
            this.loginDateNum = this.getGameDate();
            // Trace.log("开服天是",this.loginDateNum,Tools.dateFormat(this.gameServerStartTimer));
            // 开服时间0点是
        }

        /**
         * 游戏内 度过的 天数（从1起）
         */
        public function getGameDate(ms:Number = -1):Number {
            var s:Number = Math.ceil(this.getGameTime(ms) / Tools.oneDayMilli);
            // 开服时间第几天
            return s;
        }

        /**
         * 游戏内 度过的 年数（从1起）
         */
        public function getGameYear(ms:Number = -1):Number {
            var s:Number = getGameDate();
            // 开服时间第几天
            return Math.floor((s - 1) / 4);
        }

        /**
         * 游戏内 度过的 时间戳 ms 包含偏移值了
         */
        public function getGameTime(ms:Number = -1):Number {
            return ((ms > -1) ? ms : ConfigServer.getServerTimer()) - this.gameServerStartTimer;
        }

        /**
         * 游戏内 度过的 季节 0123春夏秋冬
         */
        public function getGameSeason(ms:Number = -1, onlyShow:Boolean = false):int {
            var season:int = (this.getGameDate() - 1) % 4;
            if (onlyShow && TestUtils.testSeason >= 0) {
                season = TestUtils.testSeason;
            }
            return season;
        }
        public static const season_name:Array = [Tools.getMsgById("_public149"),
            Tools.getMsgById("_public150"),
            Tools.getMsgById("_public151"),
            Tools.getMsgById("_public152")];

        /**
         * 获得 游戏内 季节名称
         */
        public function getSeasonName():String {
            return season_name[this.getGameSeason()];
        }

        /**
         * 自己的 最高 战力
         */
        public function getPower(changeClip:Boolean = false, heroNum:Number = -1):Number {
            if (this.myPowerTemp > 0 && !changeClip && heroNum < 0) {

                return this.myPowerTemp;
            }
            var arr:Array = ConfigServer.system_simple.power_herocount;
            var lv:int = this.getLv();
            var len:int = arr.length;
            var index:int = -1;
            var i:int = 0;
            for (i = 0; i < len; i++) {
                if (lv <= arr[i][0]) {
                    index = i;
                    break;
                }
            }
            index = (index < 0) ? (arr.length - 1) : index;
            // 
            len = arr[index][1];

            // zhuda添加，优先重算所有副将战力，避免副将宝物评分未及时更新
            var key:String;
            var tempHero:Object;
            var adjuHeroes:Object = {};
            var hostHeroes:Object = {};
            for (key in this.hero) {
                tempHero = this.hero[key];
                if (tempHero['commander']) {
                    adjuHeroes[key] = 1;
                } else {
                    hostHeroes[key] = 1;
                }
            }

            var md:ModelHero;
            var arrMd:Array = [];
            for (key in adjuHeroes) {
                md = ModelManager.instance.modelGame.getModelHero(key);
                arrMd.push({num: md.getPower(md.getPrepare(true)), hid: md.id});
            }
            for (key in hostHeroes) {
                md = ModelManager.instance.modelGame.getModelHero(key);
                arrMd.push({num: md.getPower(md.getPrepare(true)), hid: md.id});
            }

            if (arrMd.length > 0) {
                arrMd.sort(MathUtil.sortByKey("num", true));
            }
            len = (len > arrMd.length) ? arrMd.length : len;
            if (heroNum > -1) {
                len = (heroNum > len) ? len : heroNum;
            }
            var power:Number = 0;
            ModelHero.sBestHid = arrMd[0] ? arrMd[0].hid : "";
            ModelHero.sBestHeroArr = [];
            for (i = 0; i < len; i++) {
                power += arrMd[i].num;
                ModelHero.sBestHeroArr.push(arrMd[i].hid);
            }

            if (heroNum < 0 && power != this.myPowerTemp) {
                Trace.log("检查 我的 power = ", len, power);
                if (this.myPowerTemp > 0 && changeClip) {
                    this.myPowerChangeNum += (power - this.myPowerTemp);
                    // ViewManager.instance.clearViewEffectSpecial("_power_change_");
                    Laya.timer.clear(this, this.powerTipsClip);
                    Laya.timer.once(1000, this, this.powerTipsClip, [this.myPowerChangeNum]);
                }
                this.myPowerTemp = power;
                this.event(EVENT_USER_UPDATE_POWER, power);

            }
            return power;
        }

        private function powerTipsClip(des:Number):void {
            if (des)
                ViewManager.instance.showViewEffect(UserPowerChange.getEffect(des), 0, null, false, true, "_power_change_", false);
        }

        /**
         * 获得头像(英雄id)
         */
        public function getHead(type:int = 0):String {
            var s:String = getUserHead(this.head);
            if (type == 1)
                if (s.indexOf('_') != -1) {
                    var arr:Array = s.split("_");
                    var s1:String = arr[0];
                    var s2:String = arr[1] ? arr[1] : "0";
                    var s3:String = arr[2] ? arr[2] : "0";
                    s = s1 + "_0" + "_" + s3;
                }
            return s;
        }

        public static function getUserHead(head:*):String {
            if (head)
                return head;
            return ConfigServer.system_simple["init_user"]["head"];
        }

        /**
         * 是否可进行酒馆招募
         */
        public function isPubOpen():Boolean {
            var pub:Object = ConfigServer.pub;
            var t:Number = pub_records.free_times;
            if (Tools.isNewDay(pub_records.free_time)) {
                t = 0;
            }
            if (t < pub.hero_box1.free) {
                return true;
            }
            var prop1:Array = pub.hero_box2.prop_cost;
            if (ModelManager.instance.modelProp.isHaveItemProp(prop1[0], prop1[1])) {
                return true;
            }
            var prop2:Array = pub.hero_box3.prop_cost;
            if (ModelManager.instance.modelProp.isHaveItemProp(prop2[0], prop2[1])) {
                return true;
            }

            return false;
        }

        /**
         * 获得战功进度
         */
        public function getCreditArr():Array {
            var cur_num:Number = this.year_credit;
            var cur_lv:Number = this.credit_lv;
            var c_credit:Object = this.isMerge ? ConfigServer.credit['merge_' + ModelManager.instance.modelUser.mergeNum] : ConfigServer.credit;
            var is_add:Boolean = cur_num >= c_credit.clv_up[cur_lv];
            var max_num:Number = 0;
            var min_num:Number = 0;
            var arr:Array = [];
            var num:Number = 0;
            var last_max:Number = 0;
            if (!is_add) {

                arr = c_credit.clv_first_ratio;
                num = c_credit.clv_first[cur_lv];
                last_max = arr[arr.length - 1] * num;
                if (cur_num >= last_max) {
                    max_num = c_credit.clv_up[cur_lv];
                    min_num = last_max;
                        // return [cur_num,c_credit.clv_up[cur_lv]];
                }
            } else {
                arr = c_credit.clv_added_ratio;
                num = c_credit.clv_added[cur_lv];
                last_max = arr[arr.length - 1] * num;
                if (cur_num >= last_max) {
                    // return Tools.getMsgById("_public153");//战功已满
                    var config_rool:Number = c_credit.clv_rool_reward[cur_lv][1];
                    var rool:Number = Math.floor((cur_num - last_max) / config_rool);
                    min_num = last_max + rool * config_rool;
                    max_num = last_max + (rool + 1) * config_rool;
                        // return [-1,-1,-1];
                }
            }
            for (var i:int = arr.length - 1; i >= 0; i--) {
                var n:Number = num * arr[i];
                if (cur_num < n) {
                    max_num = n;
                    min_num = i == 0 ? 0 : num * arr[i - 1];
                } else {
                    break;
                }
            }
            // return cur_num+"/"+Math.round(max_num);
            return [Math.round(min_num), cur_num, Math.round(max_num)];
        }

        /**
         * 返回可收割的列表
         */
        public function getEstateHarverst():Array {
            var arr:Array = [];
            var config_harvest_time:Number = ConfigServer.estate.harvest_time * Tools.oneMinuteMilli;
            var now:Number = ConfigServer.getServerTimer();
            for (var i:int = 0; i < this.estate.length; i++) {
                var o:Object = this.estate[i];
                var t:Number = Tools.getTimeStamp(o.harvest_time);
                if (now - t > config_harvest_time) {
                    arr.push(i);
                }
            }
            return arr;
        }

        // 英雄管理列表
        public var mHeroManagerArr:Array = [];

        /**
         * 英雄管理列表
         */
        public function setEstateManagerArr():void {
            var arr:Array = [];
            var now:Number = ConfigServer.getServerTimer();
            for (var i:int = 0; i < this.estate.length; i++) {
                var o:Object = this.estate[i];
                if (o.active_hid) {
                    var e_time:Number = Tools.getTimeStamp(o.active_harvest_time);
                    var obj:Object = {};
                    obj["hid"] = o.active_hid;
                    obj["estateFinish"] = now >= e_time;
                    obj["sortTime"] = (now - e_time);
                    obj["endTime"] = e_time;
                    obj["sortEvent"] = o.event ? 1 : 0;
                    obj["estate_index"] = i;
                    obj["estate_obj"] = {"cid": o.city_id, "estate_index": o.estate_index};
                    obj["event_id"] = o.event ? o.event : "";
                    arr.push(obj);
                }
            }
            for (var key:String in this.visit) {
                var a:Array = this.visit[key];
                var v_time:Number = Tools.getTimeStamp(a[2]);
                if (a[0]) {
                    var obj1:Object = {};
                    obj1["hid"] = a[0];
                    obj1["estateFinish"] = now >= v_time;
                    obj1["sortTime"] = (now - v_time);
                    obj1["endTime"] = v_time;
                    obj1["sortEvent"] = a[3] ? 1 : 0;
                    obj1["visit_obj"] = {"cid": key, "hid": a[0], "o_hid": a[1]};
                    obj1["event_id"] = a[3] ? a[3] : "";
                    if (a[2] != null) {
                        arr.push(obj1);
                    }
                }
            }

            for (var ckey:String in this.city_build) {
                var city_obj:Object = this.city_build[ckey];
                for (var bkey:String in city_obj) {
                    var b_arr:Array = city_obj[bkey];
                    var b_time:Number = Tools.getTimeStamp(b_arr[1]);
                    var obj2:Object = {};
                    obj2["hid"] = b_arr[0];
                    obj2["estateFinish"] = now >= b_time;
                    obj2["sortTime"] = (now - b_time);
                    obj2["endTime"] = b_time;
                    obj2["sortEvent"] = b_arr[2] ? 1 : 0;
                    obj2["cb_obj"] = {"cid": ckey, "bid": bkey};
                    obj2["event_id"] = b_arr[2] ? b_arr[2] : "";
                    arr.push(obj2);
                }
            }

            ArrayUtils.sortOn(["sortEvent", "sortTime"], arr, true);
            mHeroManagerArr = arr;
            // return arr;
        }

        /**
         * 产业拜访建设 一键收获接口调用
         */
        public function getEstateBuildVisitReward():void {
            var arr:Array = mHeroManagerArr;
            var temp:Array = [];
            for (var i:int = 0; i < arr.length; i++) {
                var o:Object = arr[i];
                if (o.event_id == "" && o.estateFinish) {
                    if (o.estate_obj) {
                        temp.push({"estate_obj": o.estate_obj});
                    } else if (o.visit_obj) {
                        temp.push({"visit_obj": o.visit_obj});
                    } else if (o.cb_obj) {
                        temp.push({"cb_obj": o.cb_obj});
                    }
                }
            }

            NetSocket.instance.send("estate_build_visit_reward", {}, new Handler(this, function(np:NetPackage):void {
                if (np.receiveData.user.estate)
                    ModelManager.instance.modelUser.estate = np.receiveData.user.estate;
                if (np.receiveData.user.visit)
                    ModelManager.instance.modelUser.visit = np.receiveData.user.visit;

                for (var i:int = 0; i < temp.length; i++) {
                    var o:Object = temp[i];
                    if (o.estate_obj) {
                        var md:ModelEstate = ModelManager.instance.modelGame.getModelEstate(o.estate_obj.cid, o.estate_obj.estate_index);
                        md.event(ModelEstate.EVENT_ESTATE_UPDATE);
                    } else if (o.visit_obj) {
                        ModelVisit.updateData(o.visit_obj.cid);
                    } else if (o.cb_obj) {
                        ModelCityBuild.removeCityBuild(o.cb_obj.cid, o.cb_obj.bid);
                    }
                }

                ModelManager.instance.modelUser.updateData(np.receiveData);
                ModelManager.instance.modelUser.giftDictHandler(np.receiveData);
            }));
        }

        /**
         * 产业or拜访派出英雄列表
         * work_index 工作类型  产业、拜访、建造
         * filterGray 是否过滤掉不可工作的英雄
         * filterHeros 过滤掉的英雄列表
         */
        public function getMyEstateHeroArr(work_index:int, prma:* = "", filterGray:Boolean = false, filterHeros:Array = null):Array {

            var sid:String = "";
            var estate6Hids:Array = [];
            if (work_index == 0) {
                var config_estate:Object = ConfigServer.estate.estate[prma + ""];
                var s:String = (config_estate.hero_debris == 0) ? config_estate.active_get : "hero";
                sid = ModelSkill.getEstateSID(s, prma + "");
                if (prma == 6) {
                    // 围猎
                    if (ModelManager.instance.modelUser.records.estate_6_hids) {
                        var a:Array = ModelManager.instance.modelUser.records.estate_6_hids;
                        if (!Tools.isNewDay(a[0])) {
                            estate6Hids = a[1];
                        }
                    }
                }
            } else if (work_index == 1) {
                sid = ModelSkill.heroVisitSkillId; // "skill287";
            } else if (work_index == 2) {
                sid = ModelSkill.cityBuildSkillId;
            }

            var arr:Array = [];
            var hmd:ModelHero;
            var visitHeroModel:ModelHero;
            var maxDimeIndex:Number = 0; // 拜访的英雄  获得他的最强属性索引值
            var visitHid:String = (prma is String && ConfigServer.hero[prma]) ? prma : "";
            if (visitHid != "") {
                visitHeroModel = ModelManager.instance.modelGame.getModelHero(visitHid);
                maxDimeIndex = visitHeroModel.getTopDimensionalArr()[2];
            }

            for (var key:String in this.hero) {
                hmd = ModelManager.instance.modelGame.getModelHero(key);
                if (filterHeros && filterHeros.indexOf(hmd.id) != -1) {
                    continue;
                }
                if (work_index == 0 && prma + "" == "6" && hmd.rarity == 4) {
                    // 牧场 传奇英雄
                    continue;
                }

                if (filterGray) {
                    if (hmd.getWork() != "")
                        continue;
                    if (estate6Hids.indexOf(key) != -1)
                        continue;
                    if (visitHid == key)
                        continue;
                }

                var o:Object = {};

                o["hid"] = key;

                o["sortPower"] = hmd.getPower() ? hmd.getPower() : 1;
                // 相对应的技能等级
                o["sortSkill"] = (sid == "") ? 0 : ModelManager.instance.modelGame.getModelSkill(sid).getLv(hmd);
                // 默认10 产业1-6  拜访7  建造8
                o["sortBusy"] = hmd.getHeroEstate().id;

                if (work_index == 0) {
                    // 狩猎每日一次是否已进行
                    o["sortNot"] = estate6Hids.indexOf(key) == -1 ? 1 : 0;
                }

                if (work_index == 1) {
                    // 是否是自己
                    o["sortMe"] = visitHid == key ? 0 : 1;
                    // 是否宿命
                    o["sortFate"] = visitHeroModel.isMyFate(hmd.id) ? 1 : 0;
                    // 四维最高的一项
                    o["sortDim"] = hmd.getOneDimensional(maxDimeIndex);
                }

                arr.push(o);

            }
            if (work_index == 0) {
                ArrayUtils.sortOn(["sortNot",
                    "sortBusy",
                    "sortSkill",
                    "sortPower"], arr, true);
            } else if (work_index == 1) {
                ArrayUtils.sortOn(["sortBusy",
                    "sortMe",
                    "sortFate",
                    "sortSkill",
                    "sortDim",
                    "sortPower"], arr, true);
            } else if (work_index == 2) {
                ArrayUtils.sortOn(["sortBusy",
                    "sortSkill",
                    "sortPower"], arr, true);
            }
            return arr;
        }

        /**
         * （暂时用不到）
         * 是否有空闲英雄
         * work_index = 0,1,2
         * parm 根据work_index有不同的参数
         * filterHeros过滤掉的英雄

           public function hasCanWorkHero(work_index:int, prma:* = "", filterHeros:Array = null):Boolean {
           // 围猎 不能是传奇 不能是this.records.estate_6_hids中今日围猎过的
           var estate6:Boolean = work_index == 0 && prma == "6";
           var visitId:String = "";
           if(work_index == 1) {
           visitId = (prma is String && ConfigServer.hero[prma]) ? prma : "";
           }
           for (var key:String in this.hero) {
           if(filterHeros && filterHeros.indexOf(key) != -1)
           continue;
           var hmd:ModelHero = ModelManager.instance.modelGame.getModelHero(key);
           if(hmd.getWork() != "")
           continue;
           if(estate6) {
           if(hmd.rarity == 4)
           continue;
           var hids:Array;
           if (ModelManager.instance.modelUser.records.estate_6_hids) {
           var a:Array = ModelManager.instance.modelUser.records.estate_6_hids;
           if (!Tools.isNewDay(a[0])) {
           hids = a[1];
           }
           }
           if(hids && hids.indexOf(key) != -1) {
           continue;
           }
           }
           if(visitId != "") {
           if(key == visitId)
           continue;
           }

           return true;
           }
           return false;
           }
         */

        /**
         * 获取『一键施政』的产业列表
         * 优先级：牧场/古迹、港口/坊市、拜访/寻访、村落、农田、林场、矿场
         *        '6'、    '2'、     'X'、     '1'、'3'、'4'、'5'
         */
        public function fastAdminArr():Array {
            var result:Array = [];

            // 找出所有可以施政的产业（包括寻访）
            var estateArr1:Array = [];
            var estateArr2:Array = [];
            var estateArr3:Array = [];
            var estateArr4:Array = [];
            var estateArr5:Array = [];
            var estateArr6:Array = [];
            ModelEstate.estateFastAdminArr(estateArr1, estateArr2, estateArr3, estateArr4, estateArr5, estateArr6);

            var visitArr:Array = [];
            ModelVisit.visitFastAdminArr(visitArr);

            // 找出所以可以匹配的英雄
            var canWorkHeros:Array = this.getAllCanWordHeros();

            // 为产业匹配英雄
            var i:int = 0, j:int = 0;
            var existedHeros:Object = {}; // 所有可以匹配的英雄（key 为英雄 id；value=1 代表存在且未被使用，value=2代表存在且被使用）
            for (i = 0; i < canWorkHeros.length; i++) {
                existedHeros[canWorkHeros[i]] = "1";
            }

            // 3.1 为牧场/古迹匹配
            var config_estate:Object = ConfigServer.estate.estate["6"];
            var s:String = (config_estate.hero_debris == 0) ? config_estate.active_get : "hero";
            var skillId:String = ModelSkill.getEstateSID(s, "6");

            this.sortEstate(canWorkHeros, skillId);

            for (i = 0; i < estateArr6.length; i++) {
                var estate:Object = estateArr6[i];

                var cacheHeroId:String = this.getCacheHero(estate["city_id"], estate["estate_index"]);
                if (cacheHeroId != "" && existedHeros[cacheHeroId] == "1") {
                    var hmd:ModelHero = ModelManager.instance.modelGame.getModelHero(cacheHeroId);
                    var workStatus:int = hmd.checkCanWork(0, "6");
                    if (workStatus == 0) { // 既不是传奇英雄，今天也没有围猎过（可以挂机是默认的）
                        existedHeros[cacheHeroId] == "2";
                        estate["hid"] = cacheHeroId;
                        result.push(estate);
                        continue;
                    }
                }

                for (j = 0; j < canWorkHeros.length; j++) {
                    var currentHeroId:String = canWorkHeros[j];
                    if (existedHeros[currentHeroId] != "1") {
                        continue
                    }

                    hmd = ModelManager.instance.modelGame.getModelHero(currentHeroId);
                    workStatus = hmd.checkCanWork(0, "6");
                    if (workStatus == 0) {
                        existedHeros[currentHeroId] = "2";
                        estate["hid"] = currentHeroId;
                        result.push(estate);
                        break;
                    }
                }

                if (estate["hid"] == "") { // 说明匹配完成，应该直接结束匹配过程
                    return result;
                }
            }
            canWorkHeros = this.deleteUsedHeros(canWorkHeros, existedHeros); // 把已经匹配的英雄从 canWorkHeros 中删掉

            // 3.2 为港口/坊市匹配
            config_estate = ConfigServer.estate.estate["2"];
            s = (config_estate.hero_debris == 0) ? config_estate.active_get : "hero";
            skillId = ModelSkill.getEstateSID(s, "2");

            this.sortEstate(canWorkHeros, skillId);

            for (i = 0; i < estateArr2.length; i++) {
                estate = estateArr2[i];

                for (j = 0; j < canWorkHeros.length; j++) {
                    if (existedHeros[canWorkHeros[j]] == "1") {
                        existedHeros[canWorkHeros[j]] = "2";
                        estate["hid"] = canWorkHeros[j];
                        result.push(estate);
                        break
                    }
                }

                if (estate["hid"] == "") {
                    return result;
                }
            }
            canWorkHeros = this.deleteUsedHeros(canWorkHeros, existedHeros);

            // 3.3 为拜访/寻访匹配
            // 首次遍历时，只给有宿命关系的英雄进行匹配。有多个宿命英雄的话，按照『宿命>口才>拼点』排序
            for (i = 0; i < visitArr.length; i++) {
                var visit:Object = visitArr[i];
                var cityId:String = visit["city_id"];
                var visitHeroId:String = ModelManager.instance.modelGame.getModelVisit(cityId).visit_hid;

                var recommendHeroId:String = this.getVisitHero(visitHeroId, canWorkHeros, true);
                if (recommendHeroId == "") {
                    continue;
                }

                ArrayUtils.remove(recommendHeroId, canWorkHeros);
                existedHeros[recommendHeroId] = "2";
                visit["hid"] = recommendHeroId;
                result.push(visit);
            }
            // 二次遍历时，已不存在宿命关系，所以直接按照『口才>拼点』的优先级进行选取
            for (i = 0; i < visitArr.length; i++) {
                visit = visitArr[i];

                if (!Tools.isNullObj(visit["hid"]) && visit["hid"] != "") {
                    continue;
                }

                cityId = visit["city_id"];
                visitHeroId = ModelManager.instance.modelGame.getModelVisit(cityId).visit_hid;

                recommendHeroId = this.getVisitHero(visitHeroId, canWorkHeros, false);
                if (recommendHeroId == "") {
                    continue;
                }

                ArrayUtils.remove(recommendHeroId, canWorkHeros);
                existedHeros[recommendHeroId] = "2";
                visit["hid"] = recommendHeroId;
                result.push(visit);
            }

            // 3.4 为村落匹配
            config_estate = ConfigServer.estate.estate["1"];
            s = (config_estate.hero_debris == 0) ? config_estate.active_get : "hero";
            skillId = ModelSkill.getEstateSID(s, "1");

            this.sortEstate(canWorkHeros, skillId);

            for (i = 0; i < estateArr1.length; i++) {
                estate = estateArr1[i];

                for (j = 0; j < canWorkHeros.length; j++) {
                    if (existedHeros[canWorkHeros[j]] == "1") {
                        existedHeros[canWorkHeros[j]] = "2";
                        estate["hid"] = canWorkHeros[j];
                        result.push(estate);
                        break
                    }
                }

                if (estate["hid"] == "") {
                    return result;
                }
            }
            canWorkHeros = this.deleteUsedHeros(canWorkHeros, existedHeros);

            // 3.5. 为农田匹配
            config_estate = ConfigServer.estate.estate["3"];
            s = (config_estate.hero_debris == 0) ? config_estate.active_get : "hero";
            skillId = ModelSkill.getEstateSID(s, "3");

            this.sortEstate(canWorkHeros, skillId);

            for (i = 0; i < estateArr3.length; i++) {
                estate = estateArr3[i];

                for (j = 0; j < canWorkHeros.length; j++) {
                    if (existedHeros[canWorkHeros[j]] == "1") {
                        existedHeros[canWorkHeros[j]] = "2";
                        estate["hid"] = canWorkHeros[j];
                        result.push(estate);
                        break
                    }
                }

                if (estate["hid"] == "") {
                    return result;
                }
            }
            canWorkHeros = this.deleteUsedHeros(canWorkHeros, existedHeros);

            // 3.6 为林场匹配
            config_estate = ConfigServer.estate.estate["4"];
            s = (config_estate.hero_debris == 0) ? config_estate.active_get : "hero";
            skillId = ModelSkill.getEstateSID(s, "4");

            this.sortEstate(canWorkHeros, skillId);

            for (i = 0; i < estateArr4.length; i++) {
                estate = estateArr4[i];

                for (j = 0; j < canWorkHeros.length; j++) {
                    if (existedHeros[canWorkHeros[j]] == "1") {
                        existedHeros[canWorkHeros[j]] = "2";
                        estate["hid"] = canWorkHeros[j];
                        result.push(estate);
                        break
                    }
                }

                if (estate["hid"] == "") {
                    return result;
                }
            }
            canWorkHeros = this.deleteUsedHeros(canWorkHeros, existedHeros);

            // 3.7 为矿场匹配
            config_estate = ConfigServer.estate.estate["5"];
            s = (config_estate.hero_debris == 0) ? config_estate.active_get : "hero";
            skillId = ModelSkill.getEstateSID(s, "5");

            this.sortEstate(canWorkHeros, skillId);

            for (i = 0; i < estateArr5.length; i++) {
                estate = estateArr5[i];

                for (j = 0; j < canWorkHeros.length; j++) {
                    if (existedHeros[canWorkHeros[j]] == "1") {
                        existedHeros[canWorkHeros[j]] = "2";
                        estate["hid"] = canWorkHeros[j];
                        result.push(estate);
                        break
                    }
                }

                if (estate["hid"] == "") {
                    return result;
                }
            }
            canWorkHeros = this.deleteUsedHeros(canWorkHeros, existedHeros);

            return result;
        }

        // 获取所有可以执政的英雄
        private function getAllCanWordHeros():Array {
            var result:Array = [];
            for (var heroId:String in this.hero) {
                var hmd:ModelHero = ModelManager.instance.modelGame.getModelHero(heroId);
                if (hmd.getWork() == "") {
                    result.push(heroId);
                }
            }
            return result;
        }

        // 获取上一次在当前牧场/古迹中参悟的英雄
        private function getCacheHero(cityId:String, index:int):String {
            var modelEstate:ModelEstate = ModelManager.instance.modelGame.getModelEstate(cityId, index);
            return modelEstate.getLocalEstate6();
        }

        // 获取寻访的推荐英雄
        // isFate = true: 只推荐宿命英雄；isFate = false: 只推荐非宿命英雄
        private function getVisitHero(visitHeroId:String, heros:Array, isFate:Boolean = false):String {
            var resultHeroId:String = "";

            if (visitHeroId == "" || heros.length == 0) {
                return resultHeroId;
            }

            var visitHeroModel:ModelHero = ModelManager.instance.modelGame.getModelHero(visitHeroId);
            var skillId:String = ModelSkill.heroVisitSkillId;

            for (var i:int = 0; i < heros.length; i++) {
                var currentHeroId:String = heros[i];
                if (currentHeroId == visitHeroId) {
                    continue;
                }
                if ((isFate && !visitHeroModel.isMyFate(currentHeroId)) || (!isFate && visitHeroModel.isMyFate(currentHeroId))) {
                    continue;
                }

                if (resultHeroId == "") {
                    resultHeroId = currentHeroId;
                    continue;
                }

                var currentHeroModel:ModelHero = ModelManager.instance.modelGame.getModelHero(currentHeroId);
                var currentSkillLv:Number = ModelManager.instance.modelGame.getModelSkill(skillId).getLv(currentHeroModel);
                var resultHeroModel:ModelHero = ModelManager.instance.modelGame.getModelHero(resultHeroId);
                var resultSkillLv:Number = ModelManager.instance.modelGame.getModelSkill(skillId).getLv(resultHeroModel);

                if (resultSkillLv < currentSkillLv) {
                    resultHeroId = currentHeroId;
                    continue;
                } else if (resultSkillLv > currentSkillLv) {
                    continue;
                } else {
                    var visitMaxDimeIndex:Number = visitHeroModel.getTopDimensionalArr()[2];

                    var resultDime:Number = resultHeroModel.getOneDimensional(visitMaxDimeIndex);
                    var currentgDime:Number = currentHeroModel.getOneDimensional(visitMaxDimeIndex);
                    if (resultDime < currentgDime) {
                        resultHeroId = currentHeroId;
                        continue;
                    } else {
                        continue;
                    }
                }
            }

            return resultHeroId;
        }

        // 产业排序
        // 先根据技能等级倒序排列，再根据星级倒序排列
        private function sortEstate(heros:Array, skillId:String):void {
            heros.sort(function(hero0:String, hero1:String):Boolean {
                var hmd0:ModelHero = ModelManager.instance.modelGame.getModelHero(hero0);
                var skillLv0:Number = (skillId == "") ? 0 : ModelManager.instance.modelGame.getModelSkill(skillId).getLv(hmd0);
                var hmd1:ModelHero = ModelManager.instance.modelGame.getModelHero(hero1);
                var skillLv1:Number = (skillId == "") ? 0 : ModelManager.instance.modelGame.getModelSkill(skillId).getLv(hmd1);
                if (skillLv0 < skillLv1) {
                    return 1;
                } else if (skillLv0 > skillLv1) {
                    return -1;
                } else {
                    var star0:Number = hmd0.getStar();
                    var star1:Number = hmd1.getStar();
                    if (star0 < star1) {
                        return 1;
                    } else {
                        return -1;
                    }
                }
            });
        }

        // 从数组 heros 中删掉已被匹配的英雄
        private function deleteUsedHeros(heros:Array, existedHeros:Object):Array {
            var result:Array = [];
            for (var i:int = 0; i < heros.length; i++) {
                var hero:String = heros[i];
                if (existedHeros[hero] == "1") {
                    result.push(hero);
                }
            }
            return result;
        }

        public function sendSocketFastWork(arr:Array, view:* = null):void {
            var obj:Object = {"estate": [], "visit": []};
            // {"estate":[{ "estate_index":0, "hid":"0"}],
            // "visit":[{"city_id":"", "hid":""]};

            var estates:Array = [];
            var visits:Array = [];
            var needSave:Boolean = false;
            for (var i:int = 0; i < arr.length; i++) {
                var o:Object = arr[i];
                if (o.hid == "")
                    continue;
                var temp:Array = [];
                if (o["type"] == "estate") {
                    temp = obj["estate"];
                    var emd:ModelEstate = ModelManager.instance.modelGame.getModelEstate(o.city_id, o.estate_index);
                    if (emd.id == "6") {
                        emd.setLocalEstate6(o.hid);
                        needSave = true;
                    }
                    temp.push({"estate_index": emd.user_index, "hid": o.hid});
                    obj["estate"] = temp;
                    estates.push(emd);
                }
                if (o["type"] == "visit") {
                    temp = obj["visit"];
                    temp.push({"city_id": o.city_id, "hid": o.hid});
                    obj["visit"] = temp;
                    visits.push(ModelManager.instance.modelGame.getModelVisit(o.city_id));
                }
            }
            if (needSave)
                ModelEstate.saveLocalEstate6();
            // trace(obj);

            NetSocket.instance.send("fast_work", obj, new Handler(this, function(np:NetPackage):void {
                var sendData:Object = np.sendData;
                ModelManager.instance.modelUser.updateData(np.receiveData);

                for (var j:int = 0; j < estates.length; j++) {
                    if (estates[j]) {
                        (estates[j] as ModelEstate).event(ModelEstate.EVENT_ESTATE_UPDATE);
                    }
                }

                for (var k:int = 0; k < visits.length; k++) {
                    if (visits[k]) {
                        ModelVisit.updateData((visits[k] as ModelVisit).city_id);
                    }
                }
                if (view) {
                    ViewManager.instance.closePanel(view);
                }

            }));
        }

        /**
         * 该城市是否完成民情
         */
        private function isFinishFtask(cid:String):Boolean {
            if (ConfigServer.city[cid].hasOwnProperty("pctask_id")) {
                if (this.ftask) {
                    if (this.ftask.hasOwnProperty(cid)) {
                        if (this.ftask[cid][0] == -1) {
                            return true;
                        }
                    }
                }
            } else {
                Trace.log("这个城市没有民情");
                return true;
            }
            return false;
        }

        /**
         * 是否显示异邦来访组队信息
         */
        public function isShowGuildAlien():void {

        }

        /**
         * 是否完成产业挂机
         */
        public function isEstateFinish(index:int):Boolean {
            var e:Object = this.estate[index];
            if (e) {
                var now:Number = ConfigServer.getServerTimer();
                var cd:Number = Tools.getTimeStamp(e.active_harvest_time);
                if (cd <= now) {
                    return true;
                }
            }
            return false;
        }

        /**
         * 是否完成拜访
         */
        public function isVisitFinish(cid:String):Boolean {
            var e:Object = this.visit[cid];
            if (e) {
                var now:Number = ConfigServer.getServerTimer();
                var cd:Number = Tools.getTimeStamp(e[2]);
                if (cd <= now) {
                    return true;
                }
            }
            return false;
        }

        /**
         * 是否完成建造
         */
        public function isCityBuildFinish(user_cb_obj:Object):Boolean {
            var e:Object = this.city_build[user_cb_obj.cid][user_cb_obj.bid];
            if (e) {
                var now:Number = ConfigServer.getServerTimer();
                var cd:Number = Tools.getTimeStamp(e[1]);
                if (cd <= now) {
                    return true;
                }
            }
            return false;

        }

        public static function getCountryID(countryID:* = ""):int {
            if (countryID is int) {
                return countryID;
            }
            return (countryID == "") ? ModelManager.instance.modelUser.country : parseInt(countryID);
        }

        /**
         * 得到首都cid
         */
        public static function getCaptainID(countryID:* = ""):int {
            var country:int = getCountryID((country < 0 ? (country + 3) : country));
            return ConfigServer.country.country[country].capital;
        }

        /**
         * 得到首都名字
         */
        public static function getCaptainName(countryID:* = ""):String {
            var country:int = getCountryID((country < 0 ? (country + 3) : country));
            return Tools.getMsgById(ConfigServer.city[ConfigServer.country.country[country].capital].name);
        }

        /**
         * 幕府里面是否有英雄
         */
        public static function isShogunHasHero():Boolean {
            var arr:Array = ModelManager.instance.modelUser.shogun;
            for (var i:int = 0; i < arr.length; i++) {
                var o:Object = arr[i];
                if (o) {
                    for (var j:int = 0; j < o.hids.length; j++) {
                        if (o.hids[j] && o.hids[j].indexOf("hero") != -1) {
                            return true;
                        }
                    }
                }
            }
            return false;
        }

        /**
         * 获得沙盘演义次数
         */
        public function pveTimes():Array {
            var arr:Array = [];
            var total_num:Number = ConfigServer.pve.combat_times + ModelScience.func_sum_type("pve_combat_times");
            if (Tools.isNewDay(this.pve_records["combat_time"])) {
                // if(total_num<pve_records.combat_times){
                // return [pve_records.combat_times,total_num];
                // }else{
                return [total_num, total_num];
                    // }
            } else {
                var n:Number = ConfigServer.pve.combat_times_buy[0] * ModelManager.instance.modelUser.pve_records.buy_times;
                total_num += n;
                return [pve_records.combat_times, total_num];
            }
        }

        /**
         * 获得pve购买次数的数据[购买几次,剩余购买次数,花费coin值]
         */
        public function pveBuyArr():Array {
            var m:Number = this.pve_records.buy_times;
            var combat_times_buy:Array = ConfigServer.pve.combat_times_buy;
            var n:Number = (combat_times_buy.length - 1) - m;
            if (Tools.isNewDay(this.pve_records.buy_time)) {
                m = 0;
                n = combat_times_buy.length - 1;
            }
            if (n < 0) {
                n = 0;
            }
            return [combat_times_buy[0], n, combat_times_buy[m + 1]];
        }

        /**
         * 酒馆是否可以招募
         */
        public function isPubCanBuy(index:int, showText:Boolean = true):Boolean {
            var isNewDay:Boolean = Tools.isNewDay(Tools.getTimeStamp(this.pub_records.draw_time));
            var isNewFree:Boolean = Tools.isNewDay(Tools.getTimeStamp(this.pub_records.free_time));
            var draw_times:Number = ConfigServer.pub.draw_limit - this.pub_records.draw_times;
            if (!isNewDay && draw_times <= 0) {
                showText && ViewManager.instance.showTipsTxt(Tools.getMsgById("_pve_tips11")); // 次数不足
                return false;
            }
            var data:Object = ConfigServer.pub["hero_box" + (index + 1)]; // listData[index];
            if (index != 0) {
                var b:Boolean = ModelManager.instance.modelProp.isHaveItemProp(data.prop_cost[0], data.prop_cost[1]);
                if (!b) {
                    if (!Tools.isCanBuy(data.cost[0], data.cost[1], showText)) {
                        return false;
                    }
                }
            } else {
                var n1:Number = data.free + ModelManager.instance.modelInside.getBuildingModel("building005").lv;
                var n2:Number = this.pub_records.free_times;
                if (isNewDay || isNewFree) {
                    n2 = 0;
                }
                if (n2 < n1) {
                    if (!Tools.isCanBuy(data.cost[0], data.cost[1], showText)) {
                        return false;
                    }
                } else {
                    showText && ViewManager.instance.showTipsTxt(Tools.getMsgById("_public42")); // 今日次数用完
                    return false;
                }
            }
            return true;
        }

        public function troop_que_max():Number {
            return ConfigServer.system_simple.troop_que + ModelOfficeRight.func_addtroop();
        }

        /**
         * 新的一年  重置战功
         */
        public function resetCredit():void {
            if (this.getGameSeason() == 0) {
                NetSocket.instance.send("get_year_attr", {}, new Handler(this, function(np:NetPackage):void {
                    ModelManager.instance.modelUser.updateData(np.receiveData);
                }));
            }
        }

        /**
         * 获得充值列表
         */
        public function getPayList(all:Boolean = false):Array {
            var arr:Array = [];

            var salePayLock:Boolean = ModelGame.unlock(null, "sale_pay").stop;
            for (var v:String in ConfigServer.pay_config_pf) {
                if (v.indexOf("pay") < 0) {
                    continue;
                }
                var a:Array = ConfigServer.pay_config_pf[v];

                // 充值礼包用的  实际不给元宝
                if (!all && a[1] == 0) {
                    continue;
                }

                if (a[9] != null) {
                    // 不显示平台
                    if (a[9] is Array && a[9].indexOf(ConfigApp.pf) != -1) {
                        continue;
                    }
                    // 全都不显示
                    if (a[9] == 0) {
                        continue;
                    }
                }
                var o:Object = {};
                o["id"] = v; // pay_id
                o["cost"] = getPayMoney(v, a[0]); // 花费的钱
                o["cost_cfg"] = a[0]; // 单笔充值档位
                o["get"] = a[1]; // 获得的黄金
                o["icon"] = a[2]; // 图标
                o["redbag"] = [a[3], a[4]]; // 赠送国家红包的范围
                o["text"] = a[5]; // 好像没用了这个
                o["first"] = a[6]; // 首充的话给这个黄金数
                o["salePayNum"] = !salePayLock ? ModelSalePay.getNumByPId(v) : 0; // 兑换券数量
                var maxSid:String = ModelSalePay.getMaxSaleByPId(v);
                o["salePayID"] = !salePayLock ? (maxSid == v ? "" : maxSid) : ""; // 当前使用的兑换券id

                arr.push(o);
            }
            arr.sort(MathUtil.sortByKey("get", false));
            return arr;
        }

        /**
         * 每个档位不同pf下的价格
         * 默认 6、30、68、328、648
         */
        public function getPayMoney(pid:String, base:*):String {
            if (ConfigServer.system_simple.pay_money) {
                if (ConfigServer.system_simple.pay_money[ConfigApp.pf]) {
                    if (ConfigServer.system_simple.pay_money[ConfigApp.pf][pid]) {
                        return ConfigServer.system_simple.pay_money[ConfigApp.pf][pid];
                    }
                }
            }
            return base;
        }

        /**
         * 充值币种符号 + 充值金额
         * n = 充值金额
         */
        public static function getPayMoneyStr(n:*):String {
            return Tools.getMsgById('193004', [getPayCurrency(), n]);
        }

        /**
         * 充值币种符号
         */
        public static function getPayCurrency():String {
            var default_pay_currency:String = Tools.getMsgById("_lht86");
            var pay_currency:Object = ConfigServer.system_simple.pay_currency;
            if (!pay_currency) {
                return default_pay_currency;
            }
            var pf_pay_currency:String = pay_currency[ConfigApp.pf];
            return pf_pay_currency ? pf_pay_currency : default_pay_currency;
        }

        /**
         * 根据配置的充值金额（充值配置数组第0位）获取pid
         */
        public static function getPidByMoney(money:int):String {
            for (var pid:String in ConfigServer.pay_config_pf) {
                if (pid.indexOf("pay") < 0) {
                    continue;
                }
                var a:Array = ConfigServer.pay_config_pf[pid];
                if (a[0] === money) {
                    return pid;
                }
            }
            return '';
        }

        /**
         * 根据商品id返回payId
         */
        public static function getPidByGoodId(gid:String):String {
            if (ConfigServer.goods[gid]) {
                return ConfigServer.goods[gid].pay_id;
            }
            return "";
        }

        /**
         * 根据支付id 返回货币符号+金额的字符串
         */
        public static function getStrByPid(pid:String):String {
            if (ConfigServer.pay_config_pf[pid]) {
                var pMoney:String = ModelManager.instance.modelUser.getPayMoney(pid, ConfigServer.pay_config_pf[pid][0]);
                return getPayMoneyStr(pMoney);
            }
            return "";
        }

        /**
         * 根据商品id 返回货币符号+金额的字符串
         */
        public static function getStrByGid(gid:String):String {
            if (ConfigServer.goods[gid]) {
                var pid:String = ConfigServer.goods[gid].pay_id;
                return getStrByPid(pid);
            }
            return "";
        }

        /**
         * 是否是新的一年
         */
        public static function isNewYear():Boolean {
            var n:Number = ModelManager.instance.modelUser.getGameYear();
            var m:Number = ModelManager.instance.modelUser.credit_year;
            // Trace.log("服务器",m,"本地",n);
            if (n > m) {
                return true;
            }
            return false;
        }

        /**
         * 检查bug反馈红点  每次登录检查
         */
        public function checkBugMsgRedPoint():void {
            var sendData:Object = {"uid": ModelManager.instance.modelUser.mUID,
                    "sessionid": ModelManager.instance.modelUser.mSessionid,
                    "zone": ModelManager.instance.modelUser.zone};
            NetHttp.instance.send("bug_msg.get_bug_msg", sendData, Handler.create(this, function(obj:Object):void {

            }));

            // ModelManager.instance.modelChat.isNewBugMSG=true;
            // ModelManager.instance.modelUser.event(ModelUser.EVENT_USER_UPDATE,[{"user":""},true]);//通知红点刷新
            // event(ModelGame.EVENT_UPDAET_BUG_MSG);
        }

        public function recruit_hero_cb(re:NetPackage):void {
            ModelManager.instance.modelUser.updateData(re.receiveData);
            var hid:String = re.sendData.hid;
            SaveLocal.savaArr(SaveLocal.KEY_NEW_HERO + ModelManager.instance.modelUser.mUID, "heros", hid, true);
            GotoManager.boundForPanel(GotoManager.VIEW_HERO_GET_NEW, hid);
            // ViewManager.instance.showView(ConfigClass.VIEW_HERO_GET_NEW, hid);
        }

        /**
         * 获得护国军出发时间
         * 不用了  以后用getCountryArmyAriseTime2
         */
        public static function getCountryArmyAriseTime():Object {
            var o:Object;
            if (ModelOfficial.cities == null) {
                return null;
            }
            if (ModelUser.isHaveCountryArmy() == false) {
                return null;
            }

            if (ConfigServer.country_army && ConfigServer.country_army.arise_time && ConfigServer.country_army.arise_time.length > 0) {
                var now:Number = ConfigServer.getServerTimer();
                var dt:ServerDate = new ServerDate(now);

                var n:Number = dt.getHours() * 60 + dt.getMinutes();
                var arr:Array = [];
                for (var i:int = 0; i < ConfigServer.country_army.arise_time.length; i++) {
                    var a:Array = ConfigServer.country_army.arise_time[i];
                    arr.push(a[0] * 60 + a[1]);
                }
                var cid:String;
                // var citys:Array = [];//满足条件的城市
                var citys:Object = {}; // {"cid":time}
                var cfgCitys:Array = ConfigServer.country_army[ModelManager.instance.modelUser.country].target_city;

                for (var k:int = 0; k < cfgCitys.length; k++) {
                    cid = cfgCitys[k][cfgCitys[k].length - 1];
                    var n0:Number = ModelOfficial.cities[cid].not_belong_country[ModelManager.instance.modelUser.country];
                    var n1:Number = n0 ? n0 * 1000 : 0;
                    var n2:Number = ConfigServer.getServerTimer();
                    var n3:Number = ConfigServer.country_army.reaction_time * 1000;
                    // if(n1+n3<=n2){
                    // citys.push(cid);
                    // }
                    citys[cid] = (n1 == 0 || n1 + n3 <= n2) ? 0 : n3 - (n2 - n1);
                }

                var troopCitys:Array = []; // 部队还存在的城市
                for (var s:String in CountryArmy.map) {
                    var ca:CountryArmy = CountryArmy.map[s] as CountryArmy;
                    if (ca.country == ModelManager.instance.modelUser.country && troopCitys.indexOf(ca.targetCity.cityId + "") == -1) {
                        troopCitys.push(ca.targetCity.cityId + "");
                    }

                }

                var small:Number = arr[0];
                var big:Number = arr[arr.length - 1];
                var aa:Array = []; // 部队存在
                var bb:Array = []; // 部队不存在

                for (var c:String in citys) {
                    if (o == null)
                        o = {};
                    var nn:Number = citys[c] == 0 ? n : n + (citys[c] / Tools.oneMinuteMilli);

                    if (nn >= big) {
                        aa = ConfigServer.country_army.arise_time[arr.length - 1];
                        bb = ConfigServer.country_army.arise_time[0];
                        if (bb[2] != null)
                            bb[2] = 1;
                        else
                            bb.push(1);
                    } else if (nn < small) {
                        bb = ConfigServer.country_army.arise_time[0];
                        aa = ConfigServer.country_army.arise_time[arr.length - 1];
                        if (aa[2] != null)
                            aa[2] = -1;
                        else
                            aa.push(-1);
                    } else {
                        for (var j:int = 0; j < arr.length; j++) {
                            if (nn >= arr[j] && nn < arr[j + 1]) {
                                aa = ConfigServer.country_army.arise_time[j];
                                bb = ConfigServer.country_army.arise_time[j + 1];
                                if (bb[2] != null)
                                    bb[2] = 0;
                                else
                                    bb.push(0);

                                if (aa[2] != null)
                                    aa[2] = 0;
                                else
                                    aa.push(0);
                                break;
                            }
                        }
                    }
                    o[c] = (troopCitys.indexOf(c) == -1) ? bb : aa;
                }

            }
            return o;
        }

        /**
         * 新的计算护国军出发时间的方法
         */
        public static function getCountryArmyAriseTime2(countryId:* = ""):Array {
            if (ModelOfficial.cities == null) {
                return null;
            }
            if (ModelUser.isHaveCountryArmy() == false) {
                return null;
            }
            var landLostArr:Array = ModelOfficial.getCountryLandLost(countryId);
            if (landLostArr == null || landLostArr.length == 0) {
                return null;
            }
            var timeArr:Array = [];
            var now:Number = ConfigServer.getServerTimer();
            var dt:ServerDate = new ServerDate(now);

            var n:Number = dt.getHours() * 60 + dt.getMinutes();
            var arr:Array = [];
            var cfgArr:Array = ConfigServer.country_army.arise_time;
            for (var i:int = 0; i < cfgArr.length; i++) {
                var a:Array = cfgArr[i][0];
                arr.push(a[0] * 60 + a[1]);
            }
            if (n < arr[0]) {
                timeArr = [cfgArr[0][0][0], cfgArr[0][0][1], 0];
            }
            if (n >= arr[arr.length - 1] + ConfigServer.country_army.reaction_time) {
                // 明天的最早的一个时间
                timeArr = [cfgArr[0][0][0], cfgArr[0][0][1], 1];
            } else {
                for (var j:int = 0; j < arr.length; j++) {
                    var n1:Number = arr[j];
                    var n2:Number = arr[j] + ConfigServer.country_army.reaction_time;
                    var n3:Number = arr[j + 1] ? arr[j + 1] : 0;
                    if (n >= n1 && n < n2) {
                        timeArr = [cfgArr[j][0][0], cfgArr[j][0][1], 0];
                        break;
                    } else if (n >= n2 && n < n3) {
                        timeArr = [cfgArr[j + 1][0][0], cfgArr[j + 1][0][1], 0];
                        break;
                    }
                }
            }
            return timeArr;
        }

        /**
         * 是否应该有护国军
         */
        public static function isHaveCountryArmy():Boolean {
            var config:Object = ConfigServer.country_army;
            if (config == null) {
                return false;
            }

            // 开服的前几天不显示
            var isMerge:Boolean = ModelManager.instance.modelUser.isMerge;
            var loginNum:Number = ModelManager.instance.modelUser.loginDateNum;
            if (!isMerge && loginNum < config.expand_day[0])
                return false;

            if (isMerge && loginNum < config.expand_day[1])
                return false;

            var dt:ServerDate = new ServerDate(ConfigServer.getServerTimer());
            var dtn:Number = dt.getHours() * 60 + dt.getMinutes();
            var armistice:Array = ConfigServer.country_army.armistice;

            // 襄阳战当天的 armistice之前  &&  襄阳战期间 不显示
            if (ModelManager.instance.modelCountryPvp.mIsToday) {
                if (dtn < (armistice[0] * 60 + armistice)) {
                    return false;
                }
                if (ModelManager.instance.modelCountryPvp.checkActive()) {
                    return false;
                }
            }

            return true;
        }

        /**
         * 军府是否已达9级   预告有额外等级可升级
         */
        public static function isScienceBreakLv():Boolean {
            if (ConfigApp.testFightType) {
                return true;
            } else {
                var bmd:ModelBuiding = ModelManager.instance.modelInside.getBuildingModel('building003');
                if (bmd && bmd.lv >= 9) {
                    // 军府9级解锁技能上限显示
                    return true;
                }
            }
            return false;
        }

        /**
         * 奖励内容处理
         * 传入服务器返回的数据
         */
        public function giftDictHandler(receiveData:*):void {
            // gift_dict:{"id":num,"equip":["equipId"]}   double_gift :["id"]
            // gift_dict_list:[{"id",num},{"id":num}]     double_gift_list :[[],["id1","id2"]]
            var gifts:*;
            var doubleObj:Object = {};
            if (receiveData.hasOwnProperty("gift_dict")) {
                gifts = receiveData["gift_dict"];
                if (receiveData.hasOwnProperty("double_gift")) {
                    gifts["double_gift"] = receiveData["double_gift"];
                }
            } else if (receiveData.hasOwnProperty("gift_dict_list")) {
                gifts = receiveData["gift_dict_list"];
                if (receiveData.hasOwnProperty("double_gift_list")) {
                    for (var i:int = 0; i < gifts.length; i++) {
                        gifts[i]["double_gift"] = receiveData["double_gift_list"][i];
                    }
                }
            }
            ViewManager.instance.showRewardPanel(gifts);

            // if(receiveData.hasOwnProperty("double_gift")){
            // ViewManager.instance.showTipsPanel("double_gift<br>" + receiveData["double_gift"].toString());
            // }

            // if(receiveData.hasOwnProperty("double_gift_list")){
            // ViewManager.instance.showTipsPanel("double_gift_list<br>" + receiveData["double_gift_list"].toString());
            // }

        }

        /**
         * 获得沙盘演义已通关的章节索引
         */
        public static function getPassPVEChapterIndex():Number {
            var chapterObj:Array = ModelManager.instance.modelUser.pve_records.chapter;
            var arr:Array = [];
            for (var s:String in chapterObj) {
                var index:Number = Number(s.substring("chapter".length, s.length));
                arr.push({"index": index, "id": s});
            }
            if (arr.length == 0)
                return 0;

            ArrayUtils.sortOn(["index"], arr);

            for (var i:int = arr.length - 1; i >= 0; i--) {
                var stars:Object = chapterObj[arr[i].id]["star"];
                if (Tools.getDictLength(stars) == 4) {
                    return i + 1;
                }
            }

            return 0;
        }

        /**
         * 获得我的聊天背景框数据
         */
        public function getMyChatBgData():Array {
            var arr:Array = [];
            arr.push({"id": "1", "type": 0, "skin": "bar_11_3_1.png"});
            arr.push({"id": "2", "type": 0, "skin": "bar_16_2.png"});
            arr.push({"id": "3", "type": 0, "skin": "bar_11_3.png"});
            return arr;
        }

        /**
         * 检查比武大会是否报名
         * 登录的时候调用一次
         */
        public function checkMyPkYard():void {
            NetSocket.instance.send(NetMethodCfg.WS_SR_GET_MY_PK_YARD_HIDS, {}, Handler.create(this, function(np:NetPackage):void {

            }));
        }

        /**
         *  #红点相关属性重置
         *  #rtype: 'is_credit_lv_up'//战功
         */
        public function reset_red_dot_attr(rtype:String):void {
            NetSocket.instance.send("reset_red_dot_attr", {"rtype": rtype}, Handler.create(this, function(re:NetPackage):void {
                ModelManager.instance.modelUser.updateData(re.receiveData);
            }));

        }

        /**
         * 检查该城市是否可以做内政
         */
        public function checkCityInterior(cid:*, showTips:Boolean = true, showCity:Boolean = false):Boolean {
            cid = cid + "";
            var errorStr:String = "";
            if (isFinishFtask(cid) == false) {
                if (ModelCityControl.isOpen()) {
                    if (showCity) {
                        errorStr = Tools.getMsgById("city_control004_1", [ModelOfficial.getCityName(cid)]);
                    } else {
                        errorStr = Tools.getMsgById("city_control004");
                    }
                } else {
                    if (showCity) {
                        errorStr = Tools.getMsgById("_ftask_tips02", [ModelOfficial.getCityName(cid)]);
                    } else {
                        errorStr = Tools.getMsgById("_ftask_tips01");
                    }
                }
            }
            if (errorStr != "" && showTips) {
                ViewManager.instance.showTipsTxt(errorStr);
            }
            return (errorStr == "");
        }

        /**
         * use_function
         * 是否使用以下新功能
         * use_newinstall: 3
         * use_city_control: 1
         * use_pk_yard_new: 1
         * use_milepost: 1
         * use_ng_task: 1
         *
         */
        public function getUseFunctionByKey(key:String):Number {
            if (this.use_function && this.use_function[key]) {
                return this.use_function[key];
            }
            return 0;
        }

        /**
         * 先检查user_function
         * 没有的话再检查配置
         */
        public function getUseFunctionByKey2(key:String):Number {
            if (this.use_function && this.use_function.hasOwnProperty(key)) {
                return this.use_function[key];
            }
            if (ConfigServer.system_simple.hasOwnProperty(key)) {
                return ConfigServer.system_simple[key];
            }
            return 0;
        }

    }
}


