package sg.cfg
{
    public class ServerURLcfg_cb
    {
        public function ServerURLcfg_cb()
        {
            
        }
        public var appVersionTxt:String = "v_1_0"
        public var appFunVersion:String = "6";
        public var CC_test:Object = { // 本地开发测试用
                
                http_url: HelpConfig.serverAddress || 'http://*************:8500/gateway/',


                // http_url: 'http://cbsh1.ptkill.com/gateway/',
                // area_check:"http://gw.niuwank.com/ccc/"
                // http_url: 'http://res-kol.r2game.com/gateway/'
                // net_cfg_url: 'http://sg3.ptkill.com/gateway/',
                // http_url: 'http://hk.ptkill.com/gateway/',
                // http_url:'http://************/gateway/'
                // assets_base_url:"http://cdn2.ptkill.com/static/h5/meng52/",
                // assets_version_url:"http://cdn2.ptkill.com/static/h5/meng52/",
                // net_cfg_url:"http://sg.ptkill.com/static/h5/"
                // ping_url:"http://************/ping.html",
                "copyright": "抵制不良游戏,拒绝盗版游戏。注意自我保护,谨防受骗上当。适度游戏益脑,沉迷游戏伤身。合理安排时间,享受健康生活。",
                'des': "测试配置"};
        public var CC_server:Object = {
            // http_url: 'http://192.168.1.128:8888/gateway/',		//龚子敬的服务器三七苹果
            // http_url: 'http://192.168.3.98:8500/gateway/',
            // http_url: 'http://sg3.ptkill.com/gateway/',
            // http_url: 'http://hk.ptkill.com/gateway/',
            // http_url: 'http://cbsh1.ptkill.com/gateway/',
            // assets_base_url:"http://192.168.1.66/bin/h5/",
            // assets_version_url:"http://d25tqlozljq1fr.cloudfront.net/static/h5/tw_hk/"
                net_cfg_url: "http://cdn-cb.hzzhangyou.com/static/cb/",
                // http_url:'http://************/gateway/'
                'des': "标准配置"};
        public var CC_server_sanq:Object = {
                net_cfg_url: "http://cb.iwy23.com/static/cb/",
                // http_url:'http://************/gateway/'
                'des': "标准配置"};
        public var CC_server_sanq_kr:Object = {
                net_cfg_url: "http://cbkr.37games.com/static/cb/",
                // http_url:'http://************/gateway/'
                'des': "标准配置"};    
        public var CC_server_just4fun_cn:Object = {
                net_cfg_url: "http://sgfy.just4fun.sg/static/cb/",
                // http_url:'http://************/gateway/'
                'des': "标准配置"};  
        public var CC_server_gzsg_tw:Object = {
                net_cfg_url: "http://cb-cdn.sanguoslg.com/static/gzsg/",
                // http_url:'http://************/gateway/'
                'des': "标准配置"}; 
        public var CC_server_viet:Object = {
                net_cfg_url: "http://sgz2-cdn.muugamevn.com/static/cb/",
                // http_url:'http://************/gateway/'
                'des': "标准配置"};                                                             
        public var CC_server_qqdt_cn:Object = {
                net_cfg_url: "http://cbtx.17yqy.cn/static/h5/",
                'des': ""};
        public var CC_server_r2_kr:Object = {
                net_cfg_url: "http://cdn-zqe.guru-game.com/static/",
                'des': ""};
        public var CC_server_r2_tw:Object = {
                net_cfg_url: "http://cdn-zqe-xmgat.guru-game.com/static/",
                'des': ""};
    }
}