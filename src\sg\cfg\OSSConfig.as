package sg.cfg
{
    public class OSSConfig
    {
        // OSS资源根路径，支持动态赋值
        // public static var OSS_BASE_URL:String = "https://binglin-tx.oss-cn-hangzhou.aliyuncs.com/game/";
        public static var OSS_BASE_URL:String = "http://www.123.207.73.232.123.207.73.232/game/";
        // 全局开关，控制是否所有资源都从OSS获取
        public static var OSShuoqu:Boolean = false;

        // 设置OSS根路径（可在运行时调用）
        public static function setOSSBaseUrl(url:String):void {
            OSS_BASE_URL = url;
        }

        // 统一资源路径获取函数（保留兼容性）
        public static function getFinalResourceUrl(url:String):String
        {
            // 只用basePath控制OSS/本地，所有加载点都用相对路径
            return url;
        }
    }
} 