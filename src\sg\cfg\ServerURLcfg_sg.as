package sg.cfg
{
    public class ServerURLcfg_sg
    {
        public function ServerURLcfg_sg()
        {
            
        }
        public var appVersionTxt:String = "v_6_0"
        public var appFunVersion:String = "9";
        public var CC_test:Object = { // 本地开发测试用
                http_url: HelpConfig.serverAddress || 'http://[你的腾讯云服务器IP]:8500/gateway/',
                // http_url: 'http://qh.ptkill.com/gateway/',
                // area_check:"http://gw.niuwank.com/ccc/"
                // http_url: 'http://res-kol.r2game.com/gateway/'
                // net_cfg_url: 'http://sg3.ptkill.com/gateway/',
                // http_url: 'http://hk.ptkill.com/gateway/',
                // http_url:'http://************/gateway/'
                // assets_base_url:"http://cdn2.ptkill.com/static/h5/meng52/",
                // assets_version_url:"http://cdn2.ptkill.com/static/h5/meng52/",
                // net_cfg_url:"http://sg.ptkill.com/static/h5/"
                "copyright": "抵制不良游戏,拒绝盗版游戏。注意自我保护,谨防受骗上当。\n适度游戏益脑,沉迷游戏伤身。合理安排时间,享受健康生活。",
                'des': "测试配置"}; 
        public var CC_test_local:Object = { // 本地开发测试手机版本配置
                http_url: 'http://**************:8500/gateway/',
                // area_check:"http://gw.niuwank.com/ccc/"
                // http_url: 'http://res-kol.r2game.com/gateway/'
                // http_url: 'http://sg3.ptkill.com/gateway/'
                // http_url: 'http://hk.ptkill.com/gateway/',
                // http_url:'http://************/gateway/'
                assets_base_url: "http://192.168.1.66/release/test/",
                assets_version_url: "http://192.168.1.66/release/test/",
                // net_cfg_url:"http://sg.ptkill.com/static/h5/"
                'des': "测试配置"};
        public var CC_server:Object = {
            // http_url: 'http://192.168.1.128:8888/gateway/',		//龚子敬的服务器三七苹果
            // http_url: 'http://hk.ptkill.com:9988/gateway/',
            // http_url: 'http://192.168.101.121:8500/gateway/',
            // http_url: 'http://sg3.ptkill.com/gateway/',
            // http_url: 'https://hk.ptkill.com/gateway/',
            // http_url: 'http://hk.ptkill.com:9987/gateway/',
            // http_url: 'http://qh.ptkill.com/gateway/',
            // assets_base_url:"http://192.168.1.66/bin/h5/",
            // assets_version_url:"http://d25tqlozljq1fr.cloudfront.net/static/h5/tw_hk/"
                net_cfg_url:"http://cdn-sg.fle078.com/static/h5/",
                // http_url:'http://************/gateway/'
                'des': "标准配置"};
        public var CC_server_kr:Object = {
            // http_url: 'http://192.168.101.121:8500/gateway/',
                net_cfg_url: "http://r2cdn-kol.r2game.com/static/h5/",
                //net_cfg_url: "http://r2cdn.r2game.com/static/h5/",
                // http_url: 'http://res-kol.r2game.com/gateway/'
                'des': "标准配置-韩国"}
        public var CC_server_jp:Object = {
            // http_url: 'http://192.168.101.121:8500/gateway/',
                net_cfg_url: "http://cdn-zqwz.gamer10.com/static/h5/",
                // http_url: 'http://res-kol.r2game.com/gateway/'
                'des': "标准配置-日本"}
        public var CC_server_viet:Object = {
            // http_url: 'http://192.168.101.121:8500/gateway/',
                net_cfg_url: "http://zcwz-cdn.muugamevn.com/static/h5/",
                // http_url: 'http://res-kol.r2game.com/gateway/'
                'des': "标准配置-越南"}
        public var CC_server_th:Object = {
            // http_url: 'http://192.168.101.121:8500/gateway/',
                net_cfg_url: "http://sg-hc-tha.muugamevn.com/static/h5/",
                // http_url: 'http://res-kol.r2game.com/gateway/'
                'des': "标准配置-泰国"}
        public var CC_server_arab:Object = {
            // http_url: 'http://192.168.101.121:8500/gateway/',
                net_cfg_url: "http://zq-zy.menagame1.com/static/h5/",
                // http_url: 'http://res-kol.r2game.com/gateway/'
                'des': "标准配置-阿拉伯"}
        public var CC_server_tw_y5wa:Object = {net_cfg_url: "http://cdn-hztz.wakool.net/static/h5/",
            // http_url: 'http://res-kol.r2game.com/gateway/'
                'des': "标准配置-港澳台-大象"}
        public var CC_server_mj1:Object = {net_cfg_url: "http://cdn2.ptkill.com/static/h5/",
            // http_url: 'http://res-kol.r2game.com/gateway/'
                'des': "标准配置-mj1"}
        public var CC_server_ea37:Object = {net_cfg_url: "http://cdn-sg-ea37.hwrescdn.com/static/ea/",
            // http_url: 'http://res-kol.r2game.com/gateway/'
                'des': ""}
        public var CC_server_ea37_tw:Object = {net_cfg_url: "http://cdn-tw37sg.gm99.com/static/tw/",
            // http_url: 'http://res-kol.r2game.com/gateway/'
                'des': ""}
        public var CC_server_qqdt_cn:Object = {net_cfg_url: "http://tx-web.tongght.com/static/h5/",
            // http_url: 'http://res-kol.r2game.com/gateway/'
                'des': ""} 
        public var CC_server_ind:Object = {net_cfg_url: "http://w3k-sgcdn.indofungames.com/static/h5/",
            // http_url: 'http://res-kol.r2game.com/gateway/'
                'des': ""}     
        public var CC_server_sg4bugu:Object = {net_cfg_url: "http://cdn1-sg4.loveyugame.com/static/h5/",
        // http_url: 'http://res-kol.r2game.com/gateway/'
            'des': ""}                                            
    }
}